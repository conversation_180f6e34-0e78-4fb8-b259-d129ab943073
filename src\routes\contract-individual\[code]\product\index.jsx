import {
	<PERSON><PERSON>,
	Col,
	DEFAULT_LIMIT,
	DEFAULT_PAGE,
	ErrorMessage,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, Show } from "solid-js";
import { A, createRouteData, redirect, useLocation, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ProductFormFilter } from "~/containers/ProductFormFilter";
import { ProductTable } from "~/containers/ProductTable";
import { getProductMapList } from "~/services/product/product-map.client";
import { getContractAnnexList, getContractList } from "~/services/tender/contract.client";
import { getLotList } from "~/services/tender/lot.client";
import { getProductList } from "~/services/tender/product.client";
import AddIcon from "~icons/mdi/plus";
import styles from "../styles.module.css";
import { PRODUCT_CONTRACT_TYPE } from "~/services/tender/contract.model";


export function routeData({ location, params }) {
	// const params = useParams();
	return createRouteData(
		async ([query]) => {
			const contractCode = params.code;

			if (contractCode === "") {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

            const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			// Prepare query data
			if (contractCode) {
				q.contractCode = contractCode;
			}

			const resProduct = await getProductList({
				q,
                offset,
                limit,
				option: {
                    total: true,
                },
			});
			
			let contractIDs = [];
            let productIDs = [];
			let annexIDs = [];

			(resProduct.data || [])?.forEach((product) => {
				productIDs = productIDs.concat(product.productID || []);
				if (product.contractType === PRODUCT_CONTRACT_TYPE.MAIN_CONTRACT) {
					contractIDs = contractIDs.concat(product.contractID || []);
				} else if (product.contractType === PRODUCT_CONTRACT_TYPE.CONTRACT_ANNEX) {
					annexIDs = annexIDs.concat(product.contractID || []);
				}
			});
			
			contractIDs = Array.from(new Set(contractIDs));
			annexIDs = Array.from(new Set(annexIDs));
            productIDs = Array.from(new Set(productIDs));

            const [resContractList, resProductMap, resAnnexList] = await Promise.all([
                await getContractList({
                    q: {contractIDs},
                    offset: 0,
                    limit: 1000,
                    option: {}
                }),
                await getProductMapList({
					productIDs
                }),
				await getContractAnnexList({
                    q: {annexIDs},
                    offset: 0,
                    limit: 1000,
                    option: {}
                }),
            ]);
			

			const contractMap = {};
			(resContractList.data || []).forEach(
				(item) => (contractMap[item.contractID] = item)
			);

            const productMap = {};
			(resProductMap.data || []).forEach(
				(item) => (productMap[item.productID] = item)
			);

			(resAnnexList.data || []).forEach(
				(item) => (contractMap[item.annexID] = item)
			);
			
			return {
				products: resProduct.data,
				total: resProduct.total,
				contractMap,
				productMap,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	const params = useParams();
	return (
		<AppLayout
			pageTitle="contract:list_product"
			namespaces={["contract"]}
			breadcrumbs={[
				BREADCRUMB.CONTRACT,
				{
					label: "Danh sách sản phẩm",
					link: `/contract-individual/${params.code}`,
				}
			]}
		>
			<PageListProduct/>
		</AppLayout>
	);
};

function PageListProduct() {
	const { t } = useTranslate();
	const toast = useToast();
	const getData = useRouteData();
	const params = useParams();
	const location = useLocation();
	var patternContractProduct = /^\/contract\/[a-zA-Z0-9]+\/product$/;
	var patternContractInfo = /^\/contract\/[a-zA-Z0-9]+$/;

	return (
		<Show when={getData()}>
			<Row class="gap-3">
				<Col xs={12}>
					<div class="d-flex align-items-center justify-content-between">
						<h1 class="page-title">{t`contract:list_product`}</h1>
					</div>
				</Col>
				<Col xs={12}>
					<A 
						href={`/contract-individual/${params.code}`}
						classList={{
							[styles["link"]]: true,
							[styles["active"]]: patternContractInfo.test(location.pathname),
						}}
						class={styles["link"]}
					>Thông tin</A>
					{/* <AuthContent privilege={PRIVILEGE.BID.VIEW_LOT}> */}
						<A 
							href={`/contract-individual/${params.code}/product`}
							classList={{
								[styles["link"]]: true,
								[styles["active"]]: patternContractProduct.test(location.pathname),
							}}
							class={styles["link"]}
						>Sản phẩm</A>
					{/* </AuthContent> */}
				</Col>
			</Row>
			<div class="mt-2">
				<Show when={getData()}>
					<Row class="gap-3">
						{/* <Col xs={12}>
							<div class="d-flex align-items-center justify-content-between">
								<h1 class="page-title"></h1>
								<Button
									color="success"
									href="/product/new"
									startIcon={<AddIcon class="fs-4" />}
								>
									{t`contract:add_product`}
								</Button>
							</div>
						</Col> */}
						<br/>
						<Col xs={12}>
							<ErrorBoundary fallback={ErrorMessage}>
								<ProductTable 
									link="contract-individual"
									isViewDetail={true} 
									products={getData()?.products} 
									contractMap={getData()?.contractMap}
									productMap={getData()?.productMap}
									total={getData()?.total}/>
							</ErrorBoundary>
						</Col>
					</Row>
				</Show>
			</div>
		</Show>
	);
}