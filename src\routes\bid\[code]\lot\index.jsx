import {
	Col,
	DEFAULT_LIMIT,
	DEFAULT_PAGE,
	ErrorMessage,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, Show } from "solid-js";
import { createRouteData, redirect, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getBid, getBidLotList } from "~/services/tender/bid.client";
import { EditBidFormHeader } from "../EditBidFormHeader";
import { LotsTable } from "../LotsTable";

export function routeData({ location, params }) {
	// const params = useParams();
	return createRouteData(
		async ([query]) => {
			const bidID = params.code;

			if (!bidID) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

            const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			// Prepare query data
			q.bidID = +bidID;

			const resBid = await getBid({
				bidID,
				option: {},
			});
			if (resBid.status === API_STATUS.NOT_FOUND) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			const resLot = await getBidLotList({
				q,
				offset,
				limit,
				option: {
                    total: true,
                },
			});
			return {
				bid: resBid.data[0],
				lots: resLot.data,
				total: resLot.total,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="bid:edit_bid"
			namespaces={["bid"]}
			breadcrumbs={[BREADCRUMB.BID, BREADCRUMB.EDIT_BID]}
		>
			<PageBidLot/>
		</AppLayout>
	);
};

function PageBidLot() {
	const getData = useRouteData();

	return (
		<div class="mt-2">
			<Show when={getData()}>
				<Row class="gap-3">
					<EditBidFormHeader bidInfo={getData()} tab="lot"/>
					<Col xs={12}>
						<ErrorBoundary fallback={ErrorMessage}>
							<LotsTable tab="lot" bid={getData()?.bid} lots={getData()?.lots} total={getData()?.total}/>
						</ErrorBoundary>
					</Col>
				</Row>
			</Show>
		</div>
	);
}