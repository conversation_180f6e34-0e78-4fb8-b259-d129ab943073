import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>dal<PERSON><PERSON><PERSON>,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { ModalProps } from "@buymed/solidjs-component/src/components/modal/Modal";
import { JSXElement, Show, createSignal, splitProps } from "solid-js";
import CheckIcon from "~icons/mdi/check";
import WindowCloseIcon from "~icons/mdi/window-close";

export interface ConfirmModalProps extends ModalProps {
	/** A callback function that return an element to trigger opening the modal dialog. The element must call `openModal` */
	trigger: (openModal) => JSXElement;

	/** A callback function that return a footer element. The element can call `onClose` */
	footer?: (onClose) => JSXElement;

	/** The default open state when initially rendered. Useful when you do not need to control the open state. */
	defaultOpen?: boolean;

	/** The content for ModalHeader */
	title?: string;

	/**
	 * Callback when clicking OK button. Can be an async function .
	 * If you specify `footer`, this will not be used, you must call onOK and set isSubmitting yourself.
	 */
	onOK?: () => Promise<void>;

	/** The label for the OK button */
	okLabel?: string;

	/** Callback when clicking CLOSE button */
	onClose?: () => void;

	/** The label for the CLOSE button */
	closeLabel?: string;
}

export default function ConfirmModal(props: ConfirmModalProps): JSXElement {
	const [local, other] = splitProps(props, [
		"children",
		"closeLabel",
		"defaultOpen",
		"footer",
		"okLabel",
		"onClose",
		"onOK",
		"title",
		"trigger",
	]);
	const { t } = useTranslate();
	const [open, setOpen] = createSignal(local.defaultOpen);
	const [isSubmitting, setIsSubmitting] = createSignal(false);

	function openModal(e: Event) {
		setOpen(true);
	}

	async function onOK() {
		setIsSubmitting(true);
		local.onOK && (await local.onOK());
		setIsSubmitting(false);

		setOpen(false);
	}

	function onClose() {
		setOpen(false);
		local.onClose?.();
	}

	return (
		<>
			{local.trigger(openModal)}

			<Modal
				onClose={onClose}
				visible={open()}
				transition
				scrollable
				alignment="center"
				{...other}
			>
				<Show when={local.title}>
					<ModalHeader closeButton={false}>
						<ModalTitle>{local.title}</ModalTitle>
					</ModalHeader>
				</Show>
				<ModalBody>{local.children}</ModalBody>
				<ModalFooter>
					<Show
						when={!!local.footer}
						fallback={
							<div class="d-flex gap-2">
								<Button
									color="secondary"
									variant="outline"
									onClick={onClose}
									disabled={isSubmitting()}
									startIcon={<WindowCloseIcon font-size="1.5rem" />}
								>
									{local.closeLabel || t`common:button.cancel`}
								</Button>
								<Button
									color="success"
									onClick={onOK}
									loading={isSubmitting()}
									startIcon={<CheckIcon font-size="1.5rem" />}
								>
									{local.okLabel || t`common:button.confirm`}
								</Button>
							</div>
						}
					>
						{local.footer(onClose)}
					</Show>
				</ModalFooter>
			</Modal>
		</>
	);
}
