{
	"compilerOptions": {
		"allowSyntheticDefaultImports": true,
		"baseUrl": "./",
		"checkJs": true,
		"esModuleInterop": true,
		"isolatedModules": true,
		"jsx": "preserve",
		"jsxImportSource": "solid-js",
		"lib": ["ES6", "DOM", "ESNext"],
		"noEmit": true,
		"target": "ESNext",
		"module": "ESNext",
		"moduleResolution": "node",
		"paths": {
			"~/*": ["./src/*"],
		},
		"plugins": [{ "name": "typescript-plugin-css-modules" }],
		"types": ["unplugin-icons/types/solid", "vite/client"],
	},
	"exclude": ["node_modules", "**/node_modules/*", ".solid", ".output", ".vinxi", "dist"],
}
