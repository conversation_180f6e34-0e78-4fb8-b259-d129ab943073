import { getCookie } from "@buymed/solidjs-component/utils";
import i18nConfig from "../../i18n.config";

export function loadLocale(lang) {
	if (!lang) {
		return i18nConfig.defaultLocale;
	}
	return lang;
}

export async function loadNamespaces(namespaces, lang = getCookie("lang")) {
	const lg = loadLocale(lang);

	let res = {};
	for (let ns of namespaces) {
		res[ns] = await import(`../locales/${lg}/${ns}.json`).then((m) => m.default);
	}
	return res;
}
