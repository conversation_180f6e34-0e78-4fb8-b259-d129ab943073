import {
    <PERSON><PERSON>,
	Col,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary, Show } from "solid-js";
import { A, createRouteData, redirect, useNavigate, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { OrderFormCreate } from "~/containers/OrderFormCreate";
import { OrderFormEdit } from "~/containers/OrderFormEdit";
import { ProductFormCreate } from "~/containers/ProductFormCreate";
import { getAddressInfo } from "~/services/address/address.client";
import { createOrder, getOrder } from "~/services/tender/order.client";
import { validateOrderForm } from "~/services/tender/order.model";
import { createProduct, getProductList } from "~/services/tender/product.client";
import MdiKeyboardReturn from '~icons/mdi/keyboard-return'

export function routeData({ location }) {
	const params = useParams();
	return createRouteData(
		async ([code]) => {
			if (!code) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			const res = await getOrder({
				code,
				option: {
                    contract: true,
                    bid: true
                },
			});

			if (res.status === API_STATUS.NOT_FOUND) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}
            const contract = res.data[0].contractInfo;
            console.log(contract)
            const exRes = await Promise.all([
                await getProductList({
                    q: {
                        contractCode: contract.code,
                        contractType: "MAIN_CONTRACT",
                    },
                    limit: 1000,
                    option: { total: true },
                }),
                await getAddressInfo({
                    countryCode: "VN",
                    wardCode: res.data[0].wardCode
                })
            ]);
            
            const resProduct = exRes[0];
            const mapProductQuantity = {};
            (resProduct.data || [])?.forEach((product) => (mapProductQuantity[product.productID] = product));

			return {
                data: res.data?.[0],
                mapProductQuantity,
            };
		},
		{
			key: () => {
				return [params.code];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="add_order"
			namespaces={["order"]}
			breadcrumbs={[BREADCRUMB.ORDER, {label: "Cập nhật đơn hàng", link: "#"}]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 
    const getData = useRouteData();

	return (
		<div class="mt-2">
            <Show when={getData()}>
                <Row class="gap-3">
                    <Col xs={12}>
                        <div class="d-flex align-items-center justify-content-between">
                            <A href={BREADCRUMB.ORDER.link}>
                                <Button color="secondary" startIcon={<MdiKeyboardReturn />} >
                                    Quay lại
                                </Button>
                            </A>
                            <h1 class="page-title">Cập nhật đơn hàng</h1>
                        </div>
                    </Col>
                </Row>
                <br/>
                <Row class="gap-3">
                    <Col xs={12}>
                        <ErrorBoundary fallback={ErrorMessage}>
                            <EditOrderInformation order={getData()?.data} mapProduct={getData()?.mapProductQuantity}/>
                        </ErrorBoundary>
                    </Col>
                </Row>
            </Show>
		</div>
	);
}

function EditOrderInformation(props) {
    const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();
	
    const hookForm = createForm({
		initialValues: {
			products: props.order.items.map(e => {
                return {
                    productID: e.productID,
                    quantity: e.quantity,
                    unit: e.unitName,
                    price: e.price,
                    amount: e.totalAmount,
                    quantityRemain: props.mapProduct[e.productID] ? props.mapProduct[e.productID].quantityRemain : 0
                }
            }),
            receiverName: props.order.receiverName,
            phoneNumber: props.order.receiverPhoneNumber,
            note: props.order.note,
            countryCode: props.order.countryCode,
            provinceCode: props.order.provinceCode,
            districtCode: props.order.districtCode,
            wardCode: props.order.wardCode,
            shippingAddress: props.order.receiverShippingAddress
		},
		validate: (values) => validateOrderForm(values, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
			
			
            const payload = data;
			payload.contractCode = payload.contract.code;
			payload.products.forEach((e) => {
                e.productID = +e.productID;
            })
			payload.products = payload.products.filter((e) => e.quantity > 0);
			payload.regionCode="-";
            console.log(payload);

            const res = await createOrder(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create product:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:create_success`);
			    navigate("/order", { replace: false });
            }

            return;
		},
	});

	return (
		<OrderFormEdit 
            hookForm={hookForm} 
            order={props.order} 
            bid={props.order.contractInfo ? props.order.contractInfo.bidInfo : {}}
            contract={props.order.contractInfo || {}}/>
	);
}