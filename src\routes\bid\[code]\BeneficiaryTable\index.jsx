import { <PERSON><PERSON>, <PERSON>, Row, Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { Index } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { LOT_STATUS_LABEL_COLOR } from "~/services/tender/lot.model";
import EditIcon from "~icons/mdi/square-edit-outline";

export function BeneficiaryTable(props) {
    const { t } = useTranslate();
    return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`bid:no`}</TableHeaderCell>
						<TableHeaderCell class="col-2">{t`bid:beneficiary.name`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:beneficiary.area`}</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.data}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`bid:beneficiary.not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(beneficiary) => <BeneficiaryTableRow item={beneficiary()} />}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

function BeneficiaryTableRow(props) {
    const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.item.id}</TableCell>
			<TableCell>{props.item.name}</TableCell>
			<TableCell>{props.item.area}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`bid:edit_bid`}>
						<Button
							class="p-2"
							variant="outline"
							color="secondary"
							startIcon={<EditIcon />}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}