import { useLocation } from "@solidjs/router";
import {
	FormSelect,
	LANGUAGE_CODE,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import Cookies from "js-cookie";
import { createEffect, createResource, onCleanup } from "solid-js";
import { useAuth } from "~/contexts/AuthContext";
import { switchEntity } from "~/services/iam/iam.client";

export const ENTITY_KEY = "entity";
export const BRANCH_KEY = "branch";
export const ENTITY_LANG_MAP = {
	KH: LANGUAGE_CODE.EN,
	VN: LANGUAGE_CODE.VN,
	TH: LANGUAGE_CODE.EN,
};

export function BMEntitySelect() {
	const { userInfo, branchData, setBranchData } = useAuth();
	const location = useLocation();
	const toast = useToast();
	const { t } = useTranslate();

	const [entityOptions] = createResource(async () => {
		// let options = JSON.parse(sessionStorage.getItem(ENTITY_KEY));
		// if (!options) {
		let options = (userInfo().entities || []).map((entity) => ({
			value: entity.entityID,
			label: entity.name,
			code: entity.code,
		}));
		options = options.filter(e => e.code != "ROOT");
		sessionStorage.setItem(ENTITY_KEY, JSON.stringify(options));
		// }

		setBranchData("entityOptions", options);

		return options;
	});

	// When entity is changed, broadcast the change to other tabs
	let entityBroadcastChannel = null;
	createEffect(() => {
		if (userInfo()?.session?.clientID) {
			entityBroadcastChannel = new BroadcastChannel(`ENTITY-${userInfo().session.clientID}`);

			entityBroadcastChannel.onmessage = (e) => {
				if (
					e.data.entityID &&
					branchData.entityID &&
					e.data.entityID !== branchData.entityID
				) {
					onSelectEntity(e.data.entityID, false);
				}
			};
		}
	});
	onCleanup(() => {
		entityBroadcastChannel?.close();
	});

	// If URL has branch query, switch to that branch + Update language accordingly
	createEffect(() => {
		if (!userInfo() || !entityOptions()) return;

		const branch = location.query[BRANCH_KEY];

		// validate: `branch` must exist in entityOptions to be able to switch
		const branchOption = entityOptions().find((e) => e.code === branch);
		if (!branchOption) return;

		const branchID = branchOption.value;

		if (branchID !== branchData.entityID) {
			Cookies.set("lang", ENTITY_LANG_MAP[branch] || LANGUAGE_CODE.VN);
			onSelectEntity(branchID);
		}
	});

	/**
	 * Save to Cookie + Switch Entity
	 * @param {number} entityID
	 * @param {boolean=} shouldBroadcast - should broadcast the change to other tabs
	 */
	async function onSelectEntity(entityID, shouldBroadcast = true) {
		const selectedEntity = entityOptions().find((e) => e.value === entityID);

		setBranchData("entityID", entityID);
		setBranchData("branch", selectedEntity?.code);

		const res = await switchEntity(entityID);
		if (res.status !== API_STATUS.OK) {
			console.error("[Error] switch Entity", res);
			return toast.error(res.message);
		}

		toast.success(t("common:switch_entity_successfully", { entity: selectedEntity?.label }));

		if (shouldBroadcast) {
			// Broadcast the change to other tabs
			entityBroadcastChannel.postMessage({ entityID });
		}

		// Reload the current route
		const url = new URL(window.location.href);
		url.searchParams.delete(BRANCH_KEY);
		window.location.href = url.pathname + url.search;
	}

	return (
		<FormSelect
			options={entityOptions()}
			onChange={(e) => onSelectEntity(+e.target.value)}
			defaultValue={branchData.entityID}
		/>
	);
}
