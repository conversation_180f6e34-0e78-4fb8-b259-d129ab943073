import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";
import { ResetPasswordInfo } from "../iam/iam.model";
import { QueryOption, Bid, BidQuery } from "./bid.model";
import { Lot, LotQuery } from "./lot.model";
import { Contract } from "./contract.model";
import { Product } from "./product.model";

const URL = "/tender/core-tender/v1";

/** Return a list of product  */
export async function getProductList(
	input?: QueryInput<ProductQuery, QueryOption>
): Promise<APIResponse<Product>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/product/list`, {
		...input,
		search: input.search ? String(input.search) : undefined,
	});
}

/** Get product info */
export async function getProduct({
	code,
	option,
}: {
	code?: string;
	option?: QueryOption;
}): Promise<APIResponse<Product>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/product`, {
		code,
		option: option ? Object.keys(option).join(",") : undefined,
	});
}

/** Create product */
export async function createProduct(data: Product): Promise<APIResponse<Product>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/product`, data);
}

/** Update product information */
export async function updateProduct(data: Partial<Product>): Promise<APIResponse<Product>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/product`, data);
}

export async function productUnitConvert(data: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/product/unit-convert`, data);
}

export async function getListProductUnitConvert(data: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/product/unit-convert`, data);
}

// ===================================================
// For QUERY
// ===================================================
export type ProductQuery = {};

export type ProductQueryOption = MasterDataOption & {};