import { MasterDataOption } from "@buymed/solidjs-component/services";

export const BID_STATUS = {
	WIN: "WIN",
	CANCEL: "CANCEL",
    FAIL: "FAIL",
    WAITING: "WAITING",
    EXPIRED: "EXPIRED"
} as const;

export const BID_STATUS_LABEL = {
	[BID_STATUS.WIN]: "bid:status.WIN",
	[BID_STATUS.CANCEL]: "bid:status.CANCEL",
    [BID_STATUS.FAIL]: "bid:status.FAIL",
    [BID_STATUS.WAITING]: "bid:status.WAITING",
    [BID_STATUS.EXPIRED]: "bid:status.EXPIRED",
} as const;

export const BID_STATUS_LABEL_COLOR = {
	[BID_STATUS.WIN]: "success",
	[BID_STATUS.CANCEL]: "danger",
    [BID_STATUS.FAIL]: "danger",
    [BID_STATUS.WAITING]: "warning",
} as const;

export const BID_STATUS_OPTIONS = (t) => [
    { value: BID_STATUS.WIN, label: t(BID_STATUS_LABEL[BID_STATUS.WIN]) },
    { value: BID_STATUS.WAITING, label: t(BID_STATUS_LABEL[BID_STATUS.WAITING]) },
    { value: BID_STATUS.FAIL, label: t(BID_STATUS_LABEL[BID_STATUS.FAIL]) },
    { value: BID_STATUS.CANCEL, label: t(BID_STATUS_LABEL[BID_STATUS.CANCEL]) },
];

export const BID_CATEGORY = {
    NEW_MEDICINE: "NEW_MEDICINE",
    HERBAL_MEDICINE: "HERBAL_MEDICINE",
    MEDICAL_SUPPLIES: "MEDICAL_SUPPLIES"
};

export const BID_CATEGORY_LABEL = {
    [BID_CATEGORY.NEW_MEDICINE]: "Tân dược",
    [BID_CATEGORY.HERBAL_MEDICINE]: "Dược liệu",
    [BID_CATEGORY.MEDICAL_SUPPLIES]: "VTYT"
};

export const BID_CATEGORY_OPTIONS = (t) => [
    { value: BID_CATEGORY.NEW_MEDICINE, label: BID_CATEGORY_LABEL[BID_CATEGORY.NEW_MEDICINE] },
    { value: BID_CATEGORY.HERBAL_MEDICINE, label: BID_CATEGORY_LABEL[BID_CATEGORY.HERBAL_MEDICINE] },
    { value: BID_CATEGORY.MEDICAL_SUPPLIES, label: BID_CATEGORY_LABEL[BID_CATEGORY.MEDICAL_SUPPLIES] },
];

export const BID_SUBMISSION_METHOD = {
    CASH: "CASH",
    LETTER_OF_BANK_GUARANTEE: "LETTER_OF_BANK_GUARANTEE"
};

export const BID_SUBMISSION_METHOD_OPTIONS = (t) => [
    { value: BID_SUBMISSION_METHOD.CASH, label: t`bid:cash` },
    { value: BID_SUBMISSION_METHOD.LETTER_OF_BANK_GUARANTEE, label: t`bid:letter_of_bank_guarantee` },
];

export const BID_SUBMISSION_METHOD_LABEL = {
    [BID_SUBMISSION_METHOD.CASH]:  `bid:cash`,
    [BID_SUBMISSION_METHOD.LETTER_OF_BANK_GUARANTEE]: `bid:letter_of_bank_guarantee`,
};

export interface Bid {
	bidID?: number;
    year?: number;
	itb: string;
	procuringEntity: string;
    bidName: string,
	mainCategory: string;
	status?: keyof typeof BID_STATUS;
	openingDate?: string;
    closingDate?: string;
    numOfProduct: number;
    bidPrice: number;
    contractExecutionPeriod?: number;
    contractExecutionPeriodKind?: string;
    contractTerminationDate?: string,
    winningQuantity?: number;
    winningPrice?: number;
    winningDecision?: string;
    securityValidity?: number;
    securitySubmissionAmount?: number;
    securitySubmissionDate?: string;
    performanceValidity?: number;
    securitySubmissionMethod?: string;
    performanceSubmissionMethod?: string;
    performanceSubmissionAmount?: number;
    performanceSubmissionDate?: string;
    region?: string;
    contractNo?: string;
    createdTime?: string;
	lastUpdatedTime?: string;
}

// ===================================================
// For QUERY
// ===================================================
export type BidQuery = {};

export type QueryOption = MasterDataOption & {
	account?: boolean;
    contract?: boolean;
    contractBalance?: boolean;
};


// ===================================================
// Validation
// ===================================================
export function validateBidForm(values, t) {
	const err: any = {};

    if (!values.invitationOfBid || values.invitationOfBid.length == 0) {
        err.invitationOfBid = t`bid:itb_is_not_empty`;
    }

    if (!values.procuringEntity || values.procuringEntity.length == 0) {
        err.procuringEntity = t`bid:procuring_entity_is_not_empty`;
    }

    if (!values.bidName || values.bidName.length == 0) {
        err.bidName = t`bid:bid_name_is_not_empty`;
    }

    // if (!values.contact || values.contact.length == 0) {
    //     err.contact = t`bid:contact_is_not_empty`;
    // }

    // if (values.contractExecutionPeriod <= 0 || values.contractExecutionPeriod > 120) {
    //     err.contractExecutionPeriod = t`bid:excution_period_is_greater_than_zero_less_than_120`;
    // }

    if (!values.region || values.region.length == 0) {
        err.region = t`bid:region_is_not_empty`;
    }

    if (values.openingDate && values.closingDate && values.openingDate >= values.closingDate) {
        err.closingDate = t`bid:closing_date_is_greater_than_opening_date`;
    }
    
    if (values.securityValidity <= 0 || values.securityValidity > 300) {
        err.securityValidity = t`bid:sec_validity_is_greater_than_zero_less_than_300`;
    }
    
    // if (values.performanceValidity <= 0 || values.performanceValidity > 300) {
    //     err.performanceValidity = t`bid:per_validity_is_greater_than_zero_less_than_300`;
    // }

    // if (values.numOfProduct <= 0 || values.numOfProduct > 1000) {
    //     err.numOfProduct = t`bid:num_of_product_is_greater_than_zero_less_than_1000`;
    // }

    // if (values.bidPrice <= 0 || values.bidPrice > 1e11) {
    //     err.bidPrice = t`bid:price_is_greater_than_zero_less_than_1e11`;
    // }

    if (values.winningQuantity && values.winningQuantity < 0) {
        err.winningQuantity = t`bid:num_of_product_is_greater_than_zero_less_than_1000`;
    }

    if (values.winningPrice && (values.winningPrice < 0 || values.winningPrice > 1e11)) {
        err.winningQuantity = t`bid:price_is_greater_than_zero_less_than_1e11`;
    }

    console.log(err);
	return err;
}