import { FormAutocomplete, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS, debounce, sortBy } from "@buymed/solidjs-component/utils";
import { createResource, createSignal, splitProps } from "solid-js";
import { getBeneficiaryList } from "~/services/tender/beneficiary.client";

export function parseBeneficiaryOption(beneficiary) {
	return {
		value: beneficiary.code,
		label: `${beneficiary.name}`,
		data: {
			id: beneficiary.beneficiaryID,
			code: beneficiary.code,
			name: beneficiary.name,
			taxCode: beneficiary.taxCode,
		},
	};
}

export function BeneficiaryAutoSelect(props) {
	const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);

	const { t } = useTranslate();
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);

	const q = {};
	if (props.status) {
		q.status = props.status;
	}

	const [beneficiaryMapOptions] = createResource(
		search,
		async (search) => {
			const res = await getBeneficiaryList(
				{
					q,
					search,
					offset: 0,
					limit: 20,
				}
			);

			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch contractMapOptions", res);
				return [];
			}

			let options = res.data.map((bid) => parseBeneficiaryOption(bid));

			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					options.push(lastOption());
				}
			}

			options = sortBy(options, "label");
			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	function onInputChange(e) {
		setSearch(e.target.value);
	}
	const debouncedOnInputChange = debounce(onInputChange, 500);

	return (
		<FormAutocomplete
			name={local.name}
			options={beneficiaryMapOptions()}
			label={t`common:beneficiary`}
			placeholder={t`common:beneficiary_search`}
			onInputChange={debouncedOnInputChange}
			isLoading={beneficiaryMapOptions.loading}
			onChange={(e) => {
				setLastOption(e);
				props.onChange(e)
			}}
			{...other}
		/>
	);
}
