import { FormAutocomplete, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS, debounce, sortBy } from "@buymed/solidjs-component/utils";
import { createResource, createSignal, splitProps } from "solid-js";
import { getContractList } from "~/services/tender/contract.client";
import { getLotList } from "~/services/tender/lot.client";
import { LOT_STATUS } from "~/services/tender/lot.model";

export function parseLotOption(lot) {
	console.log(lot);
	return {
		value: "" + lot.lotID,
		label: `${lot.lotID} - ${lot.lotName} ${lot.lotLineID ? "- Lô thứ " + lot.lotLineID : ""}`,
		data: lot,
		productID: lot.productID,
		name: lot.lotName
	};
}

export function LotSelectAutoComplete(props) {
	const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);

	const { t } = useTranslate();
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);
	
	const [lotMapOptions] = createResource(
		() => [search(), props.bidID],
		async ([search, bid]) => {
			const res = await getLotList(
				{
					q: {bidID: +bid, status: LOT_STATUS.WIN},
					search,
					offset: 0,
					limit: 100,
				}
			);

			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch lotMapOptions", res);
				return [];
			}

			let options = res.data.map((bid) => parseLotOption(bid));

			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					options.push(lastOption());
				}
			}

			options = sortBy(options, "label");
			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	function onInputChange(e) {
		setSearch(e.target.value);
	}

	const debouncedOnInputChange = debounce(onInputChange, 500);

	return (
		<FormAutocomplete
			name={local.name}
			options={lotMapOptions()}
			label={t`common:lot`}
			placeholder={t`common:lot_search`}
			onInputChange={debouncedOnInputChange}
			isLoading={lotMapOptions.loading}
			{...other}
			onChange={(e) => {
				setLastOption(e);
				props.onChange(e);
			}}
			
		/>
	);
}
