import { Row, Col } from "@buymed/solidjs-component/components";
import { For, Show, splitProps } from "solid-js";
import { A, useLocation } from "solid-start";
import AuthContent from "~/components/AuthContent";

export function HeaderDetail(props) {
	const [local] = splitProps(props, ["title", "tabs"]);
	const location = useLocation();
	const paths = () => location.pathname.split("/");
	const curPath = paths()[paths().length - 1];

	return (
		<Row class="gap-3 my-3">
			<Show when={local.title}>
				<Col xs={12}>
					<div class="d-flex align-items-center justify-content-between">
						<h1 class="page-title">{local.title}</h1>
					</div>
				</Col>
			</Show>
			<Col xs={12}>
				<For each={local.tabs}>
					{(tab) => (
						<Show
							when={tab.privilege}
							fallback={
								<A
									href={`../${tab.href}`}
									classList={{
										"link-route-page": true,
										active: curPath === tab.href,
									}}
								>
									{tab.name}
								</A>
							}
						>
							<AuthContent privilege={tab.privilege}>
								<A
									href={`../${tab.href}`}
									classList={{
										"link-route-page": true,
										active: curPath === tab.href,
									}}
								>
									{tab.name}
								</A>
							</AuthContent>
						</Show>
					)}
				</For>
			</Col>
		</Row>
	);
}
