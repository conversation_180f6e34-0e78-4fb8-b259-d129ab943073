import { createForm } from "@felte/solid";
import { A, useNavigate } from "@solidjs/router";
import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	CardHeader,
	Col,
	CountryAutocomplete,
	DateRangePicker,
	DistrictAutocomplete,
	FormCheck,
	FormInput,
	FormLabel,
	LocationProvider,
	ProvinceAutocomplete,
	Row,
	Tooltip,
	WardAutocomplete,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS, formatCurrency, formatNumber, isEmptyObject, sanitize } from "@buymed/solidjs-component/utils";
import { useSearchParams } from "solid-start/router";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import { ProductSelectAutocomplete } from "../ProductSelectAutocomplete";
import { ContractSelectAutocomplete } from "../ContractSelectAutocomplete";
import { CONTRACT_STATUS } from "~/services/tender/contract.model";
import { Index, Show, createSignal } from "solid-js";
import Mdi<PERSON>inusBox from '~icons/mdi/minus-box';
import MdiPlusBox from '~icons/mdi/plus-box';
import SaveIcon from "~icons/mdi/content-save";
import { getContract, getContractList } from "~/services/tender/contract.client";
import { getProductList, productUnitConvert } from "~/services/tender/product.client";
import Skeleton from "~/components/Skeleton";

export function OrderFormCreate(props) {
	const { t } = useTranslate();
	const navigate = useNavigate();
    const toast = useToast();
    const [isLoading, setIsLoading] = createSignal(false);
	const [searchParams, setSearchParams] = useSearchParams();

    async function fetchListProduct(contract)  {
        setIsLoading(true);

        // mapping data
        // console.log(contract);
        props.hookForm.setFields("contract", contract);
        props.hookForm.setFields("receiverName", contract.beneficiaryName);
        // props.hookForm.setFields("phoneNumber", contract.phoneNumber);
        props.hookForm.setFields("email", contract.email);
        props.hookForm.setFields("shippingAddress", contract.address);

        try {
            const [respContract, resProduct] = await Promise.all([
				getContract({
					code: contract.code,
					option: {contractBalance: true},
				}),
				getProductList({
					q: {
                        contractCode: contract.code,
                        contractType: "MAIN_CONTRACT",
                    },
                    limit: 1000,
                    option: { total: true },
				})
			]);
            
            
            if (respContract.status !== API_STATUS.OK) {
                toast.error(t("common:notify.action_fail", { error: "Contract not found" }));
            } else {
                let debtContract = respContract.data[0];
                if(!debtContract.debt) {
                    toast.error(t("common:notify.action_fail", { error: "Debt not match" }));
                    return
                }
                props.hookForm.setFields("documentDataCode", debtContract.debt.documentDataCode);
                props.hookForm.setFields("totalBalanceTemporary", debtContract.debt.totalBalanceTemporary);
                props.hookForm.setFields("totalDebtTemporary", debtContract.debt.totalDebtTemporary);
            }

            let products = [];
            (resProduct.data || [])?.forEach((product) => {
                products = products.concat({
                    productID: product.productID,
                    price: product.price,
                    unit: product.unit,
                    contractProductCode: product.code,
                    quantityRemain: product.quantityRemain
                } || []);
            });

            if (products.length > 0) {
                props.hookForm.setFields("products", products)
            }
    
            console.log(`contract code ${contract}`);
        } catch (error) {
            console.log(error);
        }
        
        setIsLoading(false);
    }

    function updateAmount(index) {
		const price = props.hookForm.data(`products.${index}.price`) || 0;
		let qty = props.hookForm.data(`products.${index}.quantity`) || 0;
        const quantityRemain = props.hookForm.data(`products.${index}.quantityRemain`) || 0;
        if(qty > quantityRemain) {
            qty = quantityRemain;
            props.hookForm.setFields(`products.${index}.quantity`, quantityRemain);
        }
		props.hookForm.setFields(`products.${index}.amount`, price * qty);
	}

	return (
        <>
            <form ref={props.hookForm.form}>
                <Card>
                    <CardBody>
                        <Row class="row-gap-3">
                            <Col xs={12} md={6} lg={4}>
                                <ContractSelectAutocomplete
                                    name="contractNo"
                                    placeholder={t`common:placeholder.under_contract`}
                                    label={t`common:label.under_contract`}
                                    status={CONTRACT_STATUS.ACTIVE}
                                    onChange={(e) => fetchListProduct(e.data)}
                                />
                            </Col>
                        
                            <Show when={props.hookForm.data("contract")}>
                                <Col xs={12} md={6} lg={4}>
                                    <br/>
                                    <FormLabel>
                                        <span style={{"font-weight": "bold"}}>Mã HĐ: </span>
                                        <span>
                                            <A href={`/contract/${props.hookForm.data("contract.code")}`}>
                                                {props.hookForm.data("contract.contractNumber")}
                                            </A>
                                        </span>
                                    </FormLabel>
                                    <br/>
                                    <FormLabel>
                                        <span style={{"font-weight": "bold"}}>Tên HĐ: </span>
                                        <span>{props.hookForm.data("contract.name")}</span>
                                    </FormLabel>
                                    <br/>
                                    <FormLabel>
                                        <span style={{"font-weight": "bold"}}>Đơn vị thụ hưởng: </span>
                                        <span>{props.hookForm.data("contract.beneficiaryName")}</span>
                                    </FormLabel>
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <br/>
                                    <FormLabel>
                                        <span style={{"font-weight": "bold"}}>Gói thầu: </span>
                                        <span>Đang cập nhật</span>
                                    </FormLabel>
                                    <br/>
                                    <FormLabel>
                                        <span style={{"font-weight": "bold"}}>Số tiền đã thực hiện: </span>
                                        <span>Đang cập nhật</span>
                                    </FormLabel>
                                    <br/>
                                    <FormLabel>
                                        <span style={{"font-weight": "bold"}}>Công nợ khả dụng: </span>
                                        <Show when={props.hookForm.data("documentDataCode")}>
                                            <Show when={props.hookForm.data("totalBalanceTemporary") == 0} fallback={
                                                <span style={{"color": "green", "font-weight": "bold"}}>{formatCurrency(props.hookForm.data("totalBalanceTemporary"))}</span>
                                            }>
                                                <span style={{"color": "red", "font-weight": "bold"}}>{formatCurrency(props.hookForm.data("totalBalanceTemporary"))}</span>
                                            </Show>
                                        </Show>
                                    </FormLabel>
                                    <br/>
                                    <FormLabel>
                                        <span style={{"font-weight": "bold"}}>Nợ tạm tính: </span>
                                        <Show when={props.hookForm.data("documentDataCode")}>
                                            <span>{formatCurrency(props.hookForm.data("totalDebtTemporary"))}</span>
                                        </Show>
                                    </FormLabel>
                                </Col>
                            </Show>
                        </Row>
                    </CardBody>
                </Card>
                <br/>
                <Show when={props.hookForm.data("contract")}>
                    <Card>
                        <CardBody>
                            <header class="section-header">Thông tin người nhận</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name={`receiverName`}
                                        id={`receiverName`}
                                        label="Người nhận"
                                        invalid={!isEmptyObject(props.hookForm.errors("receiverName"))}
                                        feedbackInvalid={props.hookForm.errors("receiverName")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name={`phoneNumber`}
                                        id={`phoneNumber`}
                                        label="Số điện thoại"
                                        invalid={!isEmptyObject(props.hookForm.errors("phoneNumber"))}
                                        feedbackInvalid={props.hookForm.errors("phoneNumber")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name={`email`}
                                        id={`email`}
                                        label="Email"
                                        disabled
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12}>
                                    <FormInput
                                        name={`note`}
                                        id={`note`}
                                        label="Ghi chú"
                                        placeholder="Nhập ghi chú"
                                    />
                                </Col>
                                
                            </Row>
                            <br/>
                            <Row class="row-gap-3">
                                <LocationProvider>
                                    <div style={{"display": "none"}}>
                                        <CountryAutocomplete
                                            name="countryCode"
                                            defaultValue={"VN"}
                                            hidden
                                        />
                                    </div>
                                    <Col xs={12} md={6} lg={4}>
                                        <ProvinceAutocomplete
                                            name="provinceCode"
                                            label="Tỉnh/thành phố"
                                            placeholder="Chọn"
                                            invalid={!isEmptyObject(props.hookForm.errors("provinceCode"))}
                                            feedbackInvalid={props.hookForm.errors("provinceCode")}
                                            required
                                        />
                                    </Col>
                                    <Col xs={12} md={6} lg={4}>
                                        <DistrictAutocomplete
                                            name="districtCode"
                                            label="Quận/huyện"
                                            placeholder="Chọn"
                                            invalid={!isEmptyObject(props.hookForm.errors("districtCode"))}
                                            feedbackInvalid={props.hookForm.errors("districtCode")}
                                            required
                                        />
                                    </Col>
                                    <Col xs={12} md={6} lg={4}>
                                        <WardAutocomplete
                                            name="wardCode"
                                            label="Phường/xã"
                                            placeholder="Chọn"
                                            invalid={!isEmptyObject(props.hookForm.errors("wardCode"))}
                                            feedbackInvalid={props.hookForm.errors("wardCode")}
                                            required
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <FormInput
                                            name={`shippingAddress`}
                                            id={`shippingAddress`}
                                            label="Địa chỉ giao hàng"
                                            required
                                        />
                                    </Col>
                                </LocationProvider>
                            </Row>
                        </CardBody>
                    </Card>
                    <br/> 
                    <Card>
                        <CardBody>
                            <header class="section-header">Thông tin xuất hoá đơn</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormLabel>&nbsp</FormLabel>
                                    <FormCheck
                                        color="primary"
                                        name="canExportInvoice"
                                        id="canExportInvoice"
                                        label="Yêu cầu xuất hoá đơn VAT"
                                    />
                                </Col>
                            </Row>
                        </CardBody>
                    </Card>
                    <br/> 
                </Show>
                <Card>
                    <CardBody>
                        <header class="section-header">Sản phẩm</header>
                        <Show when={props.hookForm.data("products") && props.hookForm.data("products").length > 0} fallback={<Skeleton />}>
                            <Index each={props.hookForm.data("products")}>
                                {(product, index) => (
                                    <>
                                        <section class="d-flex flex-column row-gap-3">
                                            <Row class="row-gap-3">
                                                <Col xs={12} md={6} lg={3}>
                                                    <ProductSelectAutocomplete
                                                        name={`products.${index}.productID`}
                                                        id={`products.${index}.productID`}
                                                        productID={props.hookForm.data(`products.${index}.productID`)}
                                                        placeholder={t`common:placeholder.under_product`}
                                                        label={t`common:label.under_product`}
                                                        invalid={!isEmptyObject(props.hookForm.errors("product.productID"))}
                                                        feedbackInvalid={props.hookForm.errors("product.productID")}
                                                        disabled
                                                    />
                                                </Col>
                                                <Col xs={12} md={6} lg={3}>
                                                    <FormInput
                                                        name={`products.${index}.quantity`}
                                                        id={`products.${index}.quantity`}
                                                        label="Số lượng"
                                                        type="number"
                                                        text={`Đặt tối đa ` + formatNumber(props.hookForm.data(`products.${index}.quantityRemain`)) +` ` + props.hookForm.data(`products.${index}.unit`)}
                                                        onChange={() => updateAmount(index)}
                                                        endAdornment={props.hookForm.data(`products.${index}.unit`)}
                                                        invalid={!isEmptyObject(props.hookForm.errors(`products.${index}.unit`))}
                                                        feedbackInvalid={props.hookForm.errors(`products.${index}.unit`)}
                                                    />
                                                </Col>
                                                <Col xs={12} md={6} lg={3}>
                                                    <FormLabel>
                                                    Giá theo hợp đồng
                                                    </FormLabel>
                                                    <br/>
                                                    <FormLabel style={{"font-size": "14px", "margin": "5px"}}>
                                                        {props.hookForm.data(`products.${index}.price`) ? formatCurrency(props.hookForm.data(`products.${index}.price`)) : "0"}
                                                    </FormLabel>
                                                </Col>
                                                <Col xs={12} md={6} lg={3}>
                                                    <Row>
                                                        <Col xs={8}>
                                                            <FormLabel>
                                                                Thành tiền
                                                            </FormLabel>
                                                            <br/>
                                                            <FormLabel style={{"font-size": "14px", "margin": "5px"}}>
                                                                {props.hookForm.data(`products.${index}.amount`) ? formatCurrency(props.hookForm.data(`products.${index}.amount`)) : "0"}
                                                            </FormLabel>
                                                        </Col>
                                                        <Col xs={2}>
                                                            {/* <Show when={index+1 == props.hookForm.data("products").length }>
                                                                <Tooltip content="Thêm sản phẩm">
                                                                    <Button 
                                                                        color="second"
                                                                        style={{"margin-top": "15px"}}
                                                                        onClick={props.hookForm.addField(`products`, {}, props.hookForm.data('products').length)}
                                                                        startIcon={<MdiPlusBox class="fs-4" />}>
                                                                    </Button>
                                                                </Tooltip>
                                                            </Show> */}
                                                            <Show when={index+1 != props.hookForm.data("products").length }>
                                                                <Tooltip content="Xoá sản phẩm">
                                                                    <Button 
                                                                        color="second" 
                                                                        style={{"margin-top": "15px"}}
                                                                        onClick={() => props.hookForm.unsetField(`products.${index}`)}
                                                                        startIcon={<MdiMinusBox class="fs-4" />}>
                                                                    </Button>
                                                                </Tooltip>
                                                            </Show>
                                                        </Col>
                                                    </Row>
                                                </Col>
                                            </Row>
                                            <br/>
                                        </section>
                                    </>
                                )}
                            </Index>
                        </Show>
                    </CardBody>
                </Card>
                <br/>
                <Show when={props.hookForm.data("products") && props.hookForm.data("products").length > 0}>
                    <Card>
                        <CardBody>
                            <div class="submit-wrapper">
                                <Tooltip content={t`common:button.save`}>
                                    <Button color="success" class="ms-2" type="submit" disabled={isLoading()}>
                                        <SaveIcon class="fs-5" />
                                        {t`common:button.create_order`}
                                    </Button>
                                </Tooltip>
                            </div>
                        </CardBody>
                    </Card>
                </Show>
            </form>
        </>
	);
}
