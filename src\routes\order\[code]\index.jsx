import {
	<PERSON><PERSON>,
	Col,
	<PERSON><PERSON>rMessage,
	Row,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { A, useParams } from "@solidjs/router";
import { ErrorBoundary, Show } from "solid-js";
import { createRouteData, redirect, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { OrderCancelModal } from "~/containers/OrderDraftRemoveModal";
import { OrderInformation } from "~/containers/OrderInformation";
import { getAddressInfo } from "~/services/address/address.client";
import { getLotList } from "~/services/tender/lot.client";
import { getOrder } from "~/services/tender/order.client";
import MdiKeyboardReturn from '~icons/mdi/keyboard-return';
export function routeData({ location, params }) {
	return createRouteData(
		async ([code]) => {
			const orderCode = code;

			if (orderCode === "") {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			const respOrder = await getOrder({code: orderCode, option: {contract: true}})
			if (respOrder.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

			let lotIDs = [];
			let contractIDs = [];
			let annexIDs = [];

			const orderInfo = respOrder.data?.[0];
			(orderInfo.items || [])?.forEach((item) => {
				lotIDs = lotIDs.concat(item.lotID || []);
			});
			lotIDs = Array.from(new Set(lotIDs));

			const exRes = await Promise.all([
                await getLotList({
					q: {lotIDs: lotIDs}
				}),
                await getAddressInfo({
                    countryCode: "VN",
                    wardCode: orderInfo.wardCode
                })
            ]);

			const resLotMap = exRes[0];

			const lotMap = {};
			(resLotMap.data || []).forEach(
				(item) => (lotMap[item.lotID] = item)
			);


			return {
				order: respOrder.data?.[0],
				contract: respOrder.data?.[0].contractInfo,
				address: exRes[1].data?.[0],
				lotMap,
			};
		},
		{
			key: () => {
				return [params.code];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="order_information"
			namespaces={["order"]}
			breadcrumbs={[BREADCRUMB.ORDER, {label: "Thông tin đơn hàng", link: "#"}]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 
	const getData = useRouteData();
	const param = useParams();

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<div class="d-flex align-items-center justify-content-between">
						<A href={BREADCRUMB.ORDER.link}>
							<Button color="secondary" startIcon={<MdiKeyboardReturn />} >
								Quay lại
							</Button>
						</A>
					</div>
				</Col>
				<Col xs={12}>
					<div class="d-flex align-items-center justify-content-between">
						<h1 class="page-title">Thông tin đơn hàng</h1>
						<Show when={getData() && 
							getData()?.order.status == "WAIT_TO_CONFIRM" ||
							getData()?.order.status == "CONFIRMED" ||
							getData()?.order.status == "PROCESSING"
						} fallback={
							<A href={`#`}>
								<Button color="default" disabled>
									Chỉnh sửa
								</Button>
							</A>
						}>
							<div>
								<Show when={
									getData()?.order.status == "WAIT_TO_CONFIRM" || 
									getData()?.order.status == "CONFIRMED" || 
									getData()?.order.status == "PROCESSING"}>
									<OrderCancelModal
										buttonText="Huỷ đơn hàng"
										color="danger"
										orderCode={param.code}
									/> 
								</Show>
								
								<A href={`#`}>
									<Button color="default" disabled>
										Chỉnh sửa
									</Button>
								</A>
							</div>
						</Show>
					</div>
				</Col>
			</Row>
			<Row class="gap-3">
				<Col xs={12}></Col>
				<Col xs={12}>
					<Show when={getData()}>
						<ErrorBoundary fallback={ErrorMessage}>
							<PageOrderInformation 
								order={getData()?.order} 
								contract={getData()?.contract}
								address={getData()?.address}
								productMap={getData()?.lotMap}/>
						</ErrorBoundary>
					</Show>
				</Col>
			</Row>
		</div>
	);
}

function PageOrderInformation(props) {
    const { t } = useTranslate();
	
	return (
		<OrderInformation 
			order={props.order} 
			contract={props.contract}
			address={props.address}
			items={props.order.items || []} 
			productMap={props.productMap}/>
	);
}