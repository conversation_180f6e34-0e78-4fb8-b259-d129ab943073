import { FormAutocomplete, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS, debounce, sortBy } from "@buymed/solidjs-component/utils";
import { createResource, createSignal, splitProps } from "solid-js";
import { getProductMapList } from "~/services/product/product-map.client";
import { getBidList } from "~/services/tender/bid.client";

export function parseProductOption(product) {
	return {
		value: "" + product.productID,
		label: `${product.productID} - ${product.name}`,
		data: {
			id: product.productID,
			code: product.productCode,
			name: product.name,
			sku: product.sku,
			unit: product.unit,
		},
	};
}

export function ProductSelectAutocomplete(props) {
	const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);

	const { t } = useTranslate();
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);
	
	const [productMapOptions] = createResource(
		search,
		async (search) => {
			if(!search && !props.productID) {
				return [];
			}
			
			const query = {}
			if (props.mode && props.mode == "SKU") {
				query.isHaveSku = true
			}

			if(search !== "") {
				query.text = search
			} else if (props.productID) {
				query.productIDs = [props.productID];
			}

			const res = await getProductMapList(query);

			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch productMapOptions", res);
				return [];
			}

			let options = res.data.map((product) => parseProductOption(product));

			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					options.push(lastOption());
				}
			}

			options = sortBy(options, "label");
			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	function onInputChange(e) {
		setSearch(e.target.value);
	}
	const debouncedOnInputChange = debounce(onInputChange, 500);

	return (
		<FormAutocomplete
			name={local.name}
			options={productMapOptions()}
			label={t`common:product_map`}
			placeholder={t`common:product_map_search`}
			onInputChange={debouncedOnInputChange}
			isLoading={productMapOptions.loading}
			renderOption={(props, { data }) => (
				<li {...props}>
					[{data.id}] - <b>{data.name}</b>
				</li>
			)}
			onChange={(e) => {
				setLastOption(e);
			}}
			{...other}
		/>
	);
}
