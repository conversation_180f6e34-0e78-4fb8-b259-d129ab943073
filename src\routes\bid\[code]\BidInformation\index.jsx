import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ody, Col, DatePicker, EHTMLType, FileInput, FormCheck, FormInput, FormSelect, Row, Tooltip, useToast, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS, formatCurrency, isEmptyObject } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { Index, Show } from "solid-js";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import { FormTimeAdornment } from "~/components/FormTimeAdornment";
import { useAuth } from "~/contexts/AuthContext";
import { updateBid } from "~/services/tender/bid.client";
import { BID_CATEGORY_OPTIONS, BID_STATUS, BID_STATUS_OPTIONS, BID_SUBMISSION_METHOD, validateBidForm } from "~/services/tender/bid.model";
import { REGION_OPTIONS, YEAR_OPTIONS } from "~/services/tender/region.model";
import SaveIcon from "~icons/mdi/content-save";

export function BidInformation(props) {
    const { t } = useTranslate();
    const toast = useToast();
    const { documentToken } = useAuth();
    
    const { form, data, errors, setData } = createForm({
		initialValues: {
            bidID: props.data.bidID,
            invitationOfBid: props.data.itb || '',
            procuringEntity: props.data.procuringEntity,
            bidName: props.data.bidName,
            bidPrice: props.data.bidPrice,
            status: props.data.status,
            address: props.data.address,
            contact: props.data.contact,
            contractExecutionPeriod: props.data.contractExecutionPeriod,
            contractExecutionPeriodKind: props.data.contractExecutionPeriodKind ?? "MONTH",
            region: props.data.region,
            openingDate: props.data.openingDate,
            closingDate: props.data.closingDate,
            mainCategory: props.data.mainCategory,
            numOfProduct: props.data.numOfProduct,
            winningQuantity: props.data.winningQuantity,
            winningPrice: props.data.winningPrice,
            winningDecision: props.data.winningDecision,
            contractNo: props.data.contractNo,
            securityValidity: props.data.securityValidity,
            securitySubmissionMethod: props.data.securitySubmissionMethod,
            performanceValidity: props.data.performanceValidity,
            performanceSubmissionMethod: props.data.performanceSubmissionMethod,
            performanceSubmissionDate: props.data.performanceSubmissionDate,
            performanceSubmissionAmount: props.data.performanceSubmissionAmount,
            securitySubmissionDate: props.data.securitySubmissionDate,
            securitySubmissionAmount: props.data.securitySubmissionAmount,
            contractTermination: props.data.contractTerminationDate,
            attachments: props.data.attachments ? props.data.attachments.concat("") : [""],
            year: props.data.year
		},
		onSubmit: async (values) => {
            console.log(values);
            const data = JSON.parse(JSON.stringify(values));
            data.attachments = data.attachments.filter((e) => e.length > 0);
            data.year = +data.year;
            
            if(!data.contractTermination) {
                delete data.contractTermination;
            }

            if (!data.performanceSubmissionDate) {
                delete data.performanceSubmissionDate;
            }

            if (!data.securitySubmissionDate || data.securitySubmissionDate == "") {
                delete data.securitySubmissionDate;
            }

            const res = await updateBid(data);
			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Update bid:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.action_success`);
            }
		},
        validate: (values) => validateBidForm(values, t),
	});

	return (
		<Row class="gap-3">
			<form ref={form}>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin gói thầu</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="invitationOfBid"
                                        id="invitationOfBid"
                                        label="Mã gói thầu"
                                        placeholder="Nhập mã hiệu"
										disabled
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={8}>
                                    <FormInput
                                        name="bidName"
                                        id="bidName"
                                        label="Tên gói thầu"
                                        invalid={!isEmptyObject(errors("bidName"))}
										feedbackInvalid={errors("bidName")}
                                        required
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="procuringEntity"
                                        id="procuringEntity"
                                        label="Bên mời thầu"
                                        placeholder="Nhập bên mời thầu"
                                        invalid={!isEmptyObject(errors("procuringEntity"))}
										feedbackInvalid={errors("procuringEntity")}
										required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="address"
                                        id="address"
                                        label="Địa chỉ"
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="contact"
                                        id="contact"
                                        label="Thông tin liên lạc"
                                        invalid={!isEmptyObject(errors("contact"))}
										feedbackInvalid={errors("contact")}
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormTimeAdornment
                                        name="contractExecutionPeriod"
                                        id="contractExecutionPeriod"
                                        label="Thời gian thực hiện hợp đồng"
                                        type="number"
                                        kindCodeName="contractExecutionPeriodKind"
                                        invalid={!isEmptyObject(errors("contractExecutionPeriod"))}
										feedbackInvalid={errors("contractExecutionPeriod")}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="region"
                                        id="region"
                                        options={REGION_OPTIONS(t)}
                                        placeholder="Chọn"
                                        label="Khu vực"
                                        invalid={!isEmptyObject(errors("region"))}
										feedbackInvalid={errors("region")}
                                        required
                                        onchange={(e) => {
                                            // hookForm.setData("region", e.target.value);
                                        }}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="year"
                                        id="year"
                                        options={YEAR_OPTIONS()}
                                        placeholder="Chọn"
                                        label="Năm"
                                        invalid={!isEmptyObject(errors("year"))}
										feedbackInvalid={errors("year")}
                                        required
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin dự thầu</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
										label={t`bid:bid_opening_date`}
										name="openingDate"
                                        id="openingDate"
                                        format="dd/MM/yyyy"
										invalid={!isEmptyObject(errors("openingDate"))}
										feedbackInvalid={errors("openingDate")}
                                        required
									/>
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
										label={t`bid:bid_closing_date`}
										name="closingDate"
                                        id="closingDate"
                                        format="dd/MM/yyyy"
										invalid={!isEmptyObject(errors("closingDate"))}
										feedbackInvalid={errors("closingDate")}
                                        required
									/>
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name="securityValidity"
                                        id="securityValidity"
                                        label="Hiệu lực bảo đảm dự thầu"
										type="number"
                                        endAdornment={"Ngày"}
                                        invalid={!isEmptyObject(errors("securityValidity"))}
										feedbackInvalid={errors("securityValidity")}
                                        required

                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <label for="securitySubmissionMethod"></label>
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="securitySubmissionMethod"
                                        label="Tiền mặt"
                                        value="CASH"

                                    />
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="securitySubmissionMethod"
                                        label="Thư đảm bảo"
                                        value="LETTER_OF_BANK_GUARANTEE"
                                    />
                                </Col>
                                <Show when={data("securitySubmissionMethod") == BID_SUBMISSION_METHOD.CASH}>
                                    <Col xs={12} md={6} lg={3}>
                                        <FormInput
                                            name="securitySubmissionAmount"
                                            id="securitySubmissionAmount"
                                            label="Số tiền đảm bảo dự thầu"
                                            type="number"
                                            text={data("securitySubmissionAmount") ? formatCurrency(data("securitySubmissionAmount")) : ""}
                                            invalid={!isEmptyObject(errors("securitySubmissionAmount"))}
                                            feedbackInvalid={errors("securitySubmissionAmount")}
                                            
                                        />
                                    </Col>
                                    <Col xs={12} md={6} lg={3}>
                                        <DatePicker
                                            label="Thời gian hết hạn đảm bảo dự thầu"
                                            name="securitySubmissionDate"
                                            id="securitySubmissionDate"
                                            format="dd/MM/yyyy"
                                            invalid={!isEmptyObject(errors("securitySubmissionDate"))}
                                            feedbackInvalid={errors("securitySubmissionDate")}
                                        />
                                    </Col>
                                </Show>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                        <DatePicker
                                            label="Thời gian hết hạn đảm bảo hợp đồng"
                                            name="performanceSubmissionDate"
                                            id="performanceSubmissionDate"
                                            format="dd/MM/yyyy"
                                            placeholder={"Chọn"}
                                            invalid={!isEmptyObject(errors("performanceSubmissionDate"))}
                                            feedbackInvalid={errors("performanceSubmissionDate")}
                                        />
                                 </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <label for="performanceSubmissionMethod"></label>
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="performanceSubmissionMethod"
                                        label="Tiền mặt"
                                        value={BID_SUBMISSION_METHOD.CASH}

                                    />
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="performanceSubmissionMethod"
                                        label="Thư đảm bảo"
                                        value={BID_SUBMISSION_METHOD.LETTER_OF_BANK_GUARANTEE}
                                    />
                                </Col>
                                <Show when={data("performanceSubmissionMethod") == BID_SUBMISSION_METHOD.CASH}>
                                    <Col xs={12} md={6} lg={3}>
                                        <FormInput
                                            name="performanceSubmissionAmount"
                                            id="performanceSubmissionAmount"
                                            label="Số tiền đảm bảo hợp đồng"
                                            type="number"
                                            text={data("performanceSubmissionAmount") ? formatCurrency(data("performanceSubmissionAmount")) : ""}
                                            invalid={!isEmptyObject(errors("performanceSubmissionAmount"))}
                                            feedbackInvalid={errors("performanceSubmissionAmount")}
                                        />
                                    </Col>
                                </Show>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="mainCategory"
                                        id="mainCategory"
                                        label={t`bid:main_category`}
                                        options={BID_CATEGORY_OPTIONS(t)}
                                        disabled
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="numOfProduct"
                                        id="numOfProduct"
                                        label={t`bid:bid_quantity`}
                                        invalid={!isEmptyObject(errors("numOfProduct"))}
										feedbackInvalid={errors("numOfProduct")}
                                        disabled
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="bidPrice"
                                        id="bidPrice"
                                        label={t`bid:bid_price`}
                                        text={data("bidPrice") ? formatCurrency(data("bidPrice")) : ""}
                                        invalid={!isEmptyObject(errors("bidPrice"))}
										feedbackInvalid={errors("bidPrice")}
                                        disabled
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Tài liệu đính kèm</header>
                            <Row class="row-gap-3">
                                <Col xs={12} >
                                    <Index each={data("attachments")}>
                                        {(attachment, index) => (
                                            <FileInput
                                                documentToken={documentToken()}
                                                mode={EHTMLType.Documents}
                                                type={EHTMLType.Documents}
                                                extensions=".xlsx, .xls, .doc, .docx, .ppt, .pptx, .pdf"
                                                onAdd={(newFile) => {
                                                    console.log("newFile=", newFile)
                                                    setData("attachments", [
                                                        newFile.previewLink,
                                                        ...data("attachments"),
                                                    ]);
                                                }}
                                                onRemove={() =>
                                                    setData(
                                                        "attachments",
                                                        data("attachments")?.filter(
                                                            (_, i) => i !== index
                                                        )
                                                    )
                                                }
                                                value={attachment}
                                            />
                                        )}
                                    </Index>
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Show when={true}>
                    <Card>
                        <CardBody>
                            <section class="d-flex flex-column row-gap-3">
                                <header class="section-header">Kết quả dự thầu</header>
                                <Row class="row-gap-3">
                                    <Col xs={12} md={6} lg={4}>
                                        <FormSelect
                                            name="status"
                                            id="status"
                                            label={t`bid:bid_status`}
                                            options={BID_STATUS_OPTIONS(t)}
                                            disabled={props.data.status === BID_STATUS.WIN}
                                            required
                                        />
                                    </Col>
                                    <Show when={data('status') === BID_STATUS.WIN}>
                                        <Col xs={12} md={6} lg={4}>
                                            <FormInput
                                                name="winningQuantity"
                                                id="winningQuantity"
                                                type="number"
                                                disabled
                                                label={t`bid:winning_bid_quantity`}
                                                invalid={!isEmptyObject(errors("winningQuantity"))}
										        feedbackInvalid={errors("winningQuantity")}
                                            />
                                        </Col>
                                        <Col xs={12} md={6} lg={4}>
                                            <FormInput
                                                name="winningPrice"
                                                id="winningPrice"
                                                type="number"
                                                disabled
                                                text={data("winningPrice") ? formatCurrency(data("winningPrice")) : ""}
                                                label={t`bid:winning_bid_price`}
                                                invalid={!isEmptyObject(errors("winningPrice"))}
										        feedbackInvalid={errors("winningPrice")}
                                            />
                                        </Col>
                                    </Show>
                                    
                                </Row>
                                <Show when={data('status') === BID_STATUS.WIN}>
                                    <Row class="row-gap-3">
                                        <Col xs={12} md={6} lg={4}>
                                            <FormInput
                                                name="contractNo"
                                                id="contractNo"
                                                label={t`bid:contract_number`}
                                            />
                                        </Col>
                                        <Col xs={12} md={6} lg={4}>
                                            <DatePicker
                                                label="Thời gian kết thúc HĐ"
                                                name="contractTermination"
                                                id="contractTermination"
                                                format="dd/MM/yyyy"
                                                placeholder={"Chọn"}
                                            />
                                        </Col>
                                        <Col xs={12} md={6} lg={4}>
                                            <FormInput
                                                name="winningDecision"
                                                id="winningDecision"
                                                label={t`bid:winning_bid_decision`}
                                            />
                                        </Col>
                                    </Row>
                                </Show>
                                
                            </section>
                        </CardBody>
                    </Card>
                </Show>
                <br/>
                <AuthContent privilege={PRIVILEGE.BID.UPDATE}>
                    <Show when={true}>
                        <div class="submit-wrapper">
                            <Tooltip content={t`common:button.save`}>
                                <Button color="success" class="ms-2" type="submit">
                                    <SaveIcon class="fs-5" />
                                    {t`common:button.save`}
                                </Button>
                            </Tooltip>
                        </div>
                    </Show>
                </AuthContent>
            </form>
		</Row>
	);
}