import { BID_CATEGORY } from "./bid.model";

export interface Lot {
    lotID: number;
    lotLineID: number;
    productID: number;
    bidNo: string;
    bidID: number;
    lotName: string;
    lotPrice: number;
    lotFinalPrice: number;
    lotAmount: number;
    lotPriceUnit?: string; // VND
    guaranteeAmount?: number;
    groupMedicine: number; // number 1 to 9
    ingredient?: string;
    ingredientContent?: string;
    dosageForm?: string;
    packaging: string;
    unit?: string;
    registrationNo?: string;
    vendor: string;
    manufacturer: string;
    origin?: string;
    status: string;
    quantity: number;
    note?: string;
    createdTime?: string;
	lastUpdatedTime?: string;
}

export const LOT_STATUS = {
	WIN: "WIN",
    FAIL: "FAIL",
    WAITING: "WAITING"
} as const;

export const LOT_STATUS_LABEL = {
	[LOT_STATUS.WIN]: "bid:status.WIN",
    [LOT_STATUS.FAIL]: "bid:status.FAIL",
    [LOT_STATUS.WAITING]: "bid:status.WAITING",
} as const;

export const LOT_STATUS_LABEL_COLOR = {
	[LOT_STATUS.WIN]: "success",
    [LOT_STATUS.FAIL]: "danger",
    [LOT_STATUS.WAITING]: "warning",
} as const;

export const LOT_STATUS_OPTIONS = (t) => [
    { value: LOT_STATUS.WIN, label: t(LOT_STATUS_LABEL[LOT_STATUS.WIN]) },
    { value: LOT_STATUS.WAITING, label: t(LOT_STATUS_LABEL[LOT_STATUS.WAITING]) },
    { value: LOT_STATUS.FAIL, label: t(LOT_STATUS_LABEL[LOT_STATUS.FAIL]) },
];

export const DOSAGE_OPTIONS = (t) => [
    { value: "tablet", label: t`common:dosage.tablet` },
    { value: "sugar_tablet", label: t`common:dosage.sugar_tablet` },
    { value: "effervescent_tablet", label: t`common:dosage.effervescent_tablet` },
    { value: "film_tablet", label: t`common:dosage.film_tablet` },
    { value: "delayed_tablet", label: t`common:dosage.delayed_tablet` },
    { value: "sublingual_tablet", label: t`common:dosage.sublingual_tablet` },
    { value: "buccal_tablet", label: t`common:dosage.buccal_tablet` },
    { value: "granules", label: t`common:dosage.granules` },
    { value: "effervescent_granules", label: t`common:dosage.effervescent_granules` },
    { value: "hard_capsule", label: t`common:dosage.hard_capsule` },
    { value: "soft_capsule", label: t`common:dosage.soft_capsule` },
    { value: "syrup", label: t`common:dosage.syrup` },
    { value: "injection", label: t`common:dosage.injection` },
    { value: "gel", label: t`common:dosage.gel` },
    { value: "ointment", label: t`common:dosage.ointment` },
    { value: "transdermal_patches", label: t`common:dosage.transdermal_patches` },
    { value: "powder", label: t`common:dosage.powder` },
    { value: "eye", label: t`common:dosage.eye` },
    { value: "nasal", label: t`common:dosage.nasal` },
    { value: "shampoo", label: t`common:dosage.shampoo` },
    { value: "topical_solution", label: t`common:dosage.topical_solution` },
];

export const VAT_OPTIONS = (t) => [
    { value: 5, label: `5%` },
    { value: 10, label: `10%` },
];

export const UNIT_OPTIONS = (t) => [
    { value: t(`common:unit.tablet`).toLowerCase(), label: t`common:unit.tablet` },
    { value: t(`common:unit.bottle`).toLowerCase(), label: t`common:unit.bottle` },
    { value: t(`common:unit.box`).toLowerCase(), label: t`common:unit.box` },
    { value: t(`common:unit.bag`).toLowerCase(), label: t`common:unit.bag` },
    { value: t(`common:unit.pack`).toLowerCase(), label: t`common:unit.pack` },
    { value: t(`common:unit.tube`).toLowerCase(), label: t`common:unit.tube` },
    { value: t(`common:unit.jar`).toLowerCase(), label: t`common:unit.jar` },
    { value: t(`common:unit.lot`).toLowerCase(), label: t`common:unit.lot` },
    { value: t(`common:unit.crate`).toLowerCase(), label: t`common:unit.crate` },
    { value: t(`common:unit.pouch`).toLowerCase(), label: t`common:unit.pouch` },
    { value: t(`common:unit.unit`).toLowerCase(), label: t`common:unit.unit` },
    { value: t(`common:unit.kit`).toLowerCase(), label: t`common:unit.kit` },
    { value: t(`common:unit.pitcher`).toLowerCase(), label: t`common:unit.pitcher` },
    { value: t(`common:unit.container`).toLowerCase(), label: t`common:unit.container` },
    { value: t(`common:unit.cane`).toLowerCase(), label: t`common:unit.cane` },
    { value: t(`common:unit.meter`).toLowerCase(), label: t`common:unit.meter` },
    { value: t(`common:unit.kg`).toLowerCase(), label: t`common:unit.kg` },
];

export const DOSAGE_LABEL = (t, value) => {
    const data = DOSAGE_OPTIONS(t);
    return data.filter((e) => e.value == value);
};

export const GROUP_MEDICINE_OPTIONS = (t, mainCategory) => {
    const options = [];
    switch (mainCategory) {
        case BID_CATEGORY.NEW_MEDICINE: {
            for (let i = 1; i <= 5; i++) {
                options.push({ value: i, label: `Nhóm ${i}` });
            }
            break;
        }
        case BID_CATEGORY.HERBAL_MEDICINE: {
            for (let i = 1; i <= 4; i++) {
                options.push({ value: i, label: `Nhóm ${i}` });
            }
            break;
        }
    }
    
    return options;
}

// ===================================================
// For QUERY
// ===================================================
export type LotQuery = {};

// ===================================================
// Validation
// ===================================================
export function validateLotForm(values, t) {
	const err: any = {};

    if (!values.lotName || values.lotName.length == 0) {
        err.lotName = t`bid:lot_name_is_not_empty`;
    }

    if (!values.unit || values.unit.length == 0) {
        err.unit = t`bid:unit_is_required`;
    }

    // if (!values.registrationNo || values.registrationNo.length == 0) {
    //     err.registrationNo = t`bid:registration_no_is_required`;
    // }

    if (values.lotPrice <= 0 || values.lotPrice > 1e11) {
        err.lotPrice = t`bid:price_is_greater_than_zero_less_than_1e11`;
    }

    if (values.quantity <= 0) {
        err.quantity = t`bid:num_of_product_is_greater_than_zero`;
    }

    if (values.guaranteeAmount && values.guaranteeAmount < 0) {
        err.guaranteeAmount = t`bid:guarantee_amount_is_greater_than_zero`;
    }

    console.log("err:",err);
	return err;
}