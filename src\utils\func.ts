// <PERSON><PERSON> mảng thành các chunk đều nhau
export function chunkArrays<T>(arr: T[], chunkSize = 100): T[][] {
	if (!Array.isArray(arr) || chunkSize <= 0) return [];
	const results: T[][] = [];
	for (let i = 0; i < arr.length; i += chunkSize) {
		results.push(arr.slice(i, i + chunkSize));
	}
	return results;
}

/**
 * payloads: mảng các chunk (vd: từ chunkArrays)
 * callback: async (payload, result) => {}
 * limit: số request chạy song song
 */
export async function callMultilRequest<T>(
	payloads: T[],
	callback: (payload: T, result: { errors: any[]; data: any[]; stop: boolean }) => Promise<any>,
	limit = 6
) {
	const result = {
		errors: [] as any[],
		data: [] as any[],
		stop: false,
	};

	for (let i = 0; i < payloads.length && !result.stop; i += limit) {
		const batch = payloads.slice(i, i + limit);

		const settled = await Promise.allSettled(batch.map((payload) => callback(payload, result)));

		for (const s of settled) {
			if (s.status === "rejected") {
				result.errors.push(s.reason);
				result.stop = true;
			}
		}
	}

	return result;
}
