import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	FormInput,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { formatDatetime, isEmptyObject, sanitize } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { useNavigate } from "@solidjs/router";
import { useSearchParams } from "solid-start/router";
import { CONTRACT_STATUS } from "~/services/tender/contract.model";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import { ContractSelectAutocompleteV2 } from "../ContractSelectAutocompleteV2";
import { BeneficiaryAutoSelect } from "../BeneficiaryAutoSelect";
import { getOrderList } from "~/services/tender/order.client";
import { createSignal } from "solid-js";
import { formatDateYYYYMMDDHHIISS } from "~/utils/format";
import * as XLSX from "xlsx";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";
import { getContractList } from "~/services/tender/contract.client";

export function OrderFormFilter() {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const [searchParams, setSearchParams] = useSearchParams();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);
	const toast = useToast();
	
	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			setSearchParams({
				q,
				page: undefined,
				limit: undefined,
			});
		},
	});

	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	function onChangeContract(e) {}

	const returnMapDataExportRequest = async (q) => {
		const resOrder = await getOrderList({
			q,
			offset: 0,
			limit: 1000,
			option: {
				total: true,
			},
		});

		const orderRequestList = resOrder?.data || [];

		let contractCodes = [];
		(resOrder.data || [])?.forEach((order) => {
			contractCodes = contractCodes.concat(order.contractCode || []);
		});
		contractCodes = Array.from(new Set(contractCodes));
		const contractMap = {};

		if (contractCodes.length > 0) {
			const resContractList = await getContractList({
				q: {contractCodes},
				offset: 0,
				limit: 1000,
				option: {}
			});
			(resContractList.data || [])?.forEach((contract) => {
				contractMap[contract.code] = contract;
			});
		}

		return orderRequestList.map((item, index) => ({
			no: index+1, 
			contractCode: contractMap[item.contractCode] ? contractMap[item.contractCode].contractNumber : "",
			orderID: item.orderID,
			saleOrderCode: item.saleOrderCode,
			taxCode: item.invoice ? item.invoice.taxCode : "",
			companyName: item.invoice ? item.invoice.companyName : "",
			companyAddress: item.invoice ? item.invoice.companyAddress : "",
			receiverName: item.receiverName,
			receiverPhoneNumber: item.receiverPhoneNumber,
			receiverShippingAddress: item.receiverShippingAddress,
			totalAmount: item.totalAmount,
			createdTime: formatDatetime(item.createdTime, "MM/dd/yyyy"),
			deliveredTime: formatDatetime(item.deliveredTime, "MM/dd/yyyy"),
			status: item.status,
			createdByName: item.createdByName
		}));
	};

	const handleExportFile = async () => {
		setIsLoadingExport(true);
		const q = JSON.parse(searchParams.q || "{}");
		
		const data = await returnMapDataExportRequest(q);
		if (Array.isArray(data) && data.length) {
			const fileName = `Order_List_${formatDateYYYYMMDDHHIISS(
				new Date().toISOString()
			)}.xlsx`;

			const columnName = {
				no: `STT`, 
				contractCode: `Mã HĐ`,
				orderID: `Order ID`,
				saleOrderCode: `SO`,
				taxCode: `MST`,
				companyName: `Đơn vị`,
				companyAddress: `Địa chỉ doanh nghiệp`,
				receiverName: `Người nhận hàng`,
				receiverPhoneNumber: `SDT nhận hàng`,
				receiverShippingAddress: `Địa chỉ nhận hàng`,
				totalAmount: `Tổng tiền`,
				createdTime: `Ngày đặt hàng`,
				deliveredTime: `Ngày giao hàng`,
				status: `Trạng thái`,
				createdByName: `NV tạo`
			};

			const header = Object.keys(columnName).map((key) => columnName[key]);

			const dataWithHeader = [header, ...data.map((item) => Object.values(item))];

			const ws = XLSX.utils.aoa_to_sheet(dataWithHeader);

			const columnWidths = Object.keys(columnName).map((key) => {
				const columnHeader = columnName[key];
				const maxColumnDataLength = Math.max(
					...header.map((value) => (value ? value.toString().length : 0))
				);
				const maxColumnHeaderLength = columnHeader ? columnHeader.length : 0;
				return Math.max(maxColumnDataLength, maxColumnHeaderLength) * 1.2;
			});

			ws["!cols"] = columnWidths.map((width) => ({ width }));

			const wb = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, "List Orders");
			XLSX.writeFile(wb, fileName);
		} else {
			toast.error(t(`common:notify.action_fail`, {"error": "order not found"}));
		}
		setIsLoadingExport(false);
	}

	return (
		<Card>
			<CardBody>
				<form ref={form}>
					<Row class="row-gap-3">
						<Col xs={12} md={6} lg={4}>
							<FormInput
								name="orderID"
								type="search"
								label="Mã đơn hàng"
								placeholder="Mã đơn hàng"
							/>
						</Col>
						<Col xs={12} md={6} lg={4}>
							<FormInput
								name="receiverPhoneNumber"
								type="search"
								label="Số điện thoại"
								placeholder="Số điện thoại người nhận"
							/>
						</Col>
						<Col xs={12} md={6} lg={4}>
							<ContractSelectAutocompleteV2
								name="contractCode"
								placeholder={t`common:placeholder.under_contract`}
								label={t`common:label.under_contract`}
								status={CONTRACT_STATUS.ACTIVE}
								onChange={(e) => onChangeContract(e)}
							/>
						</Col>
						<Col xs={12} md={6} lg={4}>
							<BeneficiaryAutoSelect
								name="receiverCode"
								placeholder={t`common:placeholder.under_beneficiary`}
								label={t`common:label.under_beneficiary`}
								onChange={(e) => onChangeContract(e)}
							/>
						</Col>
						<Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
							<Button
								color="secondary"
								startIcon={<FilterRemoveIcon />}
								onClick={onClearFilter}
							>
								{t`common:button.clearFilter`}
							</Button>
							<Button
								color={"success"}
								loading={isLoadingExport()}
								onClick={handleExportFile}
								startIcon={<MdiMicrosoftExcel />}
							>
								{t("common:button.exportExcel")}
							</Button>
							<Button type="submit" color="success" startIcon={<MagnifyIcon />}>
								{t`common:button.applyButton`}
							</Button>
						</Col>
					</Row>
				</form>
			</CardBody>
		</Card>
	);
}
