import {
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { Show } from "solid-js";
import { createRouteData, redirect, useNavigate, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { LotFormCreate } from "~/containers/LotFormCreate";
import { getBid } from "~/services/tender/bid.client";
import { createLot } from "~/services/tender/lot.client";
import { validateLotForm } from "~/services/tender/lot.model";


export function routeData({ location }) {
	const params = useParams();
	return createRouteData(
		async ([code]) => {
			const bidID = +code;

			if (!bidID) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			const res = await getBid({
				bidID,
				option: {},
			});
			if (res.status === API_STATUS.NOT_FOUND) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}
			if (res.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

			return res.data?.[0]; // {}
		},
		{
			key: () => {
				return [params.code];
			},
		}
	);
}

export default () => {
	const params = useParams();
	const getData = useRouteData();
	
	return (
		<AppLayout
			pageTitle="bid:edit_bid"
			namespaces={["bid"]}
			breadcrumbs={[BREADCRUMB.BID,
			{
				label: "Chỉnh sửa thông tin gói thầu",
				link: `/bid/${params.code}/lot`,
			},
			{
				label: "Thêm sản phẩm",
				link: `/bid/${params.code}/lot`,
			}]}
		>
			<Show when={getData()}>
				<PageLotCreate bid={getData()}/>
			</Show>
		</AppLayout>
	);
};

function PageLotCreate(props) {
	const params = useParams();
	const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();

	const hookForm = createForm({
		initialValues: {
			lotPrice: 0,
			quantity: 0,
			lotAmount: 0,
			unit: "",
			registrationNo: "",
			volume: "",
			vat: 5,
		},
		validate: (values) => validateLotForm(values, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
			const bidID = +params.code
			const groupMedicine = +(data.groupMedicine)
			const productID = +(data.productID)
			data.vat = +(data.vat)
            
			const res = await createLot({...data, bidID, groupMedicine, productID});
			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create lot:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:create_success`);
			    navigate(`/bid/${bidID}/lot`, { replace: false });
            }
            return;
		},
	});

	return (
		<LotFormCreate hookForm={hookForm} bid={props.bid}/>
	);
}