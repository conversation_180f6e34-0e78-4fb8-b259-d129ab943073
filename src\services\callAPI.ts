import { APIResponse } from "@buymed/solidjs-component/services";
import {
	API_STATUS,
	HTTP_METHOD,
	sanitize,
	getSessionToken,
} from "@buymed/solidjs-component/utils";
import { FetchError, FetchOptions, ofetch } from "ofetch";
import { isServer } from "solid-js/web";
import { useRequest } from "solid-start/server";

/**
 * A wrapper to call fetch, prepare some default options
 * @async
 * @param method - The HTTP method
 * @param path - The API path to trigger
 * @param data - for GET/DELETE, it's the query. For POST/PUT/QUERY, it's the body
 * @param options - https://github.com/unjs/ofetch, if presented, it will override default options
 */
export async function callAPI<T>(
	method: keyof typeof HTTP_METHOD,
	path: string,
	data?: object,
	options?: FetchOptions
): Promise<APIResponse<T>> {
	// ------ Prepare options for fetch
	const fetchOptions = sanitize({
		baseURL: isServer ? import.meta.env.VITE_API_HOST : "/backend",
		method,

		// TODO: Interceptors for common error response like 401, 403...
		// https://github.com/unjs/ofetch?tab=readme-ov-file#onresponse-request-options-response-

		...options,
	});

	if (!fetchOptions.headers) {
		fetchOptions.headers = newHeaders();
	}

	if (data) {
		if (method === HTTP_METHOD.GET || method === HTTP_METHOD.DELETE) {
			fetchOptions.query = sanitize(data);
		} else {
			fetchOptions.body = JSON.stringify(sanitize(data));
		}
	}

	// ------ Call fetch
	try {
		return await ofetch(path, fetchOptions);
	} catch (e) {
		const data = (e as FetchError<APIResponse<T>>).data;

		// No need to log 404 error
		if (data?.status !== API_STATUS.NOT_FOUND) {
			console.error("[Error] fetch URL=", path, "options=", fetchOptions, e);
		}

		return data!;
	}
}

// Prepare default headers
function newHeaders(): HeadersInit {
	const headers: Record<string, string> = {};

	if (isServer) {
		const event = useRequest();
		const reqHeaders = event.request.headers;

		const token = getSessionToken(reqHeaders);
		if (token) {
			headers["Authorization"] = `Bearer ${token}`;
		}
		headers["User-Agent"] = reqHeaders?.get("user-agent") ?? "";
		headers["xForwardedFor"] = reqHeaders?.get("x-forwarded-for") ?? "";
	} else {
		headers["User-Agent"] = navigator["userAgent"];
		headers["xForwardedFor"] = (navigator as any)["xForwardedFor"];
	}

	return sanitize(headers, { removeEmptyString: true });
}
