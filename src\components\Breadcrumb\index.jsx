import { A, useLocation } from "@solidjs/router";
import { SidebarContext, useTranslate } from "@buymed/solidjs-component/components";
import { For, Show, useContext } from "solid-js";
import { BreadcrumbContext } from "~/contexts/BreadcrumbContext";
import ArrowRightIcon from "~icons/mdi/chevron-right";
import styles from "./Breadcrumb.module.scss";

function BreadcrumbItem(props) {
	const { t } = useTranslate();
	const location = useLocation();

	const href = () => (props.keepSearch ? `${props.link}${location.search}` : props.link);

	return (
		<A href={href()} class={styles["breadcrumb-items"]} classList={props.classList}>
			<Show when={!props.param} fallback={t(props.label, props.param)}>
				{t(props.label)}
			</Show>
		</A>
	);
}

export default function Breadcrumb() {
	const { breadcrumbs } = useContext(BreadcrumbContext);
	const { sidebarOpen } = useContext(SidebarContext);

	return (
		<div
			classList={{
				[styles["breadcrumb-wrapper"]]: true,
				[styles["sidebar-open"]]: !sidebarOpen(),
			}}
		>
			<For each={breadcrumbs()}>
				{({ label, link, keepSearch = false, param }, index) => (
					<>
						<BreadcrumbItem
							label={label}
							link={link}
							keepSearch={keepSearch}
							param={param}
							classList={{
								[styles["active"]]: index() === breadcrumbs().length - 1,
								[styles["disabled"]]: index() === breadcrumbs().length - 1,
							}}
						/>

						<Show when={index() < breadcrumbs().length - 1}>
							<ArrowRightIcon />
						</Show>
					</>
				)}
			</For>
		</div>
	);
}
