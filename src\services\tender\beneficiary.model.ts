export interface Beneficiary {
	id: number;
	name: string;
	area: string;
	createdTime?: string;
	lastUpdatedTime?: string;
}

export const BENEFICIARY_STATUS = {
	ACTIVE: "ACTIVE", // Đang hoạt động
	INACTIVE: "INACTIVE", // Không hoạt động
} as const;

export const BENEFICIARY_STATUS_LABEL = {
	[BENEFICIARY_STATUS.ACTIVE]: "beneficiary:status.ACTIVE",
	[BENEFICIARY_STATUS.INACTIVE]: "beneficiary:status.INACTIVE",
} as const;

export const BENEFICIARY_STATUS_LABEL_COLOR = {
	[BENEFICIARY_STATUS.ACTIVE]: "success",
	[BENEFICIARY_STATUS.INACTIVE]: "danger",
} as const;

export async function validateBeneficiaryForm(values) {
	const err = {};
	const regex = {
		number: /^[0-9]+$/,
		phone: /^0\d{9}$/,
		email: /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,
		trim: /^(?!\s)(?!.*\s$).*$/,
		taxCode: /^(?:\d{10}|\d{12}|\d{13})$/,
	};
	const messages = {
		empty: "Không được bỏ trống",
		number: "Chỉ được nhập số",
		phone: "Số điện thoại không hợp lệ",
		email: "Email không hợp lệ",
		space: "Có khoảng trắng đầu hoặc cuối",
		addressMin: "Địa chỉ phải có ít nhất 5 ký tự",
		taxCode: "Mã số thuế phải gồm 10, 12 hoặc 13 chữ số",
	};

	const checkString = (field, value, tests = []) => {
		if (!value) {
			err[field] = messages.empty;
			return;
		}
		if (!regex.trim.test(value)) {
			err[field] = messages.space;
			return;
		}
		for (const [reg, msg] of tests) {
			if (!reg.test(value)) {
				err[field] = msg;
				return;
			}
		}
	};

	if (!values.address) {
		err.address = messages.empty;
	} else if (!regex.trim.test(values.address)) {
		err.address = messages.space;
	} else if (values.address.trim().length < 5) {
		err.address = messages.addressMin;
	}
	checkString("name", values.name);
	checkString("phoneNumber", values.phoneNumber, [[regex.phone, messages.phone]]);
	checkString("taxCode", values.taxCode, [[regex.taxCode, messages.taxCode]]);
	if (values.email) checkString("email", values.email, [[regex.email, messages.email]]);

	return err;
}
