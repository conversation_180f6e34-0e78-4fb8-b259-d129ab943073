import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>18nProvider,
	Loading,
	Sidebar,
	SidebarContext,
	SidebarProvider,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { getCookie } from "@buymed/solidjs-component/utils";
import { Show, Suspense, createEffect, createMemo, createResource, useContext } from "solid-js";
import { Title } from "solid-start";
import { MENU } from "~/constants/menu";
import { useAuth } from "~/contexts/AuthContext";
import { useBreadcrumb } from "~/hook/useBreadcrumb";
import {
	getSSOLoginUriWithParams,
	getSSOLogoutUri,
	getSSOSwitchAccountUri,
} from "~/services/iam/path";
import { loadLocale, loadNamespaces } from "~/utils/locales";
import Breadcrumb from "../Breadcrumb";
import styles from "./AppLayout.module.scss";
import i18nConfig from "../../../i18n.config";
import { BMEntitySelect } from "./BMEntitySelect";

/**
 * @typedef AppLayoutProps
 * @property {string=} pageTitle - The page title
 * @property {string=} lang - The lang, if empty, automatically use the current lang
 * @property {string[]=} namespaces - The i18n namespace files to load
 * @property {string[]=} screens - The screens permission files to load
 * @property {any[]=} breadcrumbs - The breadcrumbs array object
 */

/**
 * The wrapper for Context Provider + UI Layout
 * @param {AppLayoutProps & import("solid-js").JSX.HTMLAttributes} props
 * @returns {import("solid-js").JSXElement}
 */

export default function AppLayout(props) {
	const { validateScreen, account } = useAuth();

	const [data] = createResource(async () => {
		const lang = loadLocale(getCookie("lang"));
		const namespaces = await loadNamespaces(["common", ...(props.namespaces || [])], lang);

		return {
			__lang: lang,
			__namespaces: namespaces,
		};
	});

	createEffect(() => useBreadcrumb(props.breadcrumbs));

	return (
		<Show when={account?.()} fallback={<Loading />}>
			<Show
				when={data && data() && account() && validateScreen(props.screens)}
				fallback={<Loading />}
			>
				<I18nProvider lang={data()?.__lang} namespaces={data()?.__namespaces}>
					<SidebarProvider>
						<BookmarksProvider>
							<Layout {...props} />
						</BookmarksProvider>
					</SidebarProvider>
				</I18nProvider>
			</Show>
		</Show>
	);
}

function Layout(props) {
	const { sidebarOpen } = useContext(SidebarContext);
	const { account, loggedInAccounts, userInfo } = useAuth();
	const { t } = useTranslate();
	const screens = createMemo(() => userInfo()?.screens || []);

	const pageTitle = createMemo(() =>
		props.pageTitle ? `${t(props.pageTitle)} | ${t("common:title")}` : t`common:title`
	);

	const menu = createMemo(
		() => MENU.map((item) => ({ ...item, name: t(item.name) }))
		// MENU.filter((tab) => screens()?.[0] === "/" || screens()?.includes(tab.link)).map(
		// 	(item) => ({ ...item, name: t(item.name) })
		// )
	);

	const isLocaleInConfig = i18nConfig.locales.includes(account()?.locale);

	return (
		<>
			<Title>{pageTitle()}</Title>

			<div
				classList={{
					[styles["layout"]]: true,
					[styles["sidebar-open"]]: sidebarOpen(),
				}}
			>
				<Header
					menu={menu()}
					logoImg="/images/buymed_logo_v2.svg"
					logoLabel="/images/buymed_v2.svg"
					label="Tender"
					getSSOLoginUri={getSSOLoginUriWithParams}
					getSSOLogoutUri={getSSOLogoutUri}
					switchAccountUrl={getSSOSwitchAccountUri()}
					account={account}
					loggedInAccounts={loggedInAccounts}
					isLocaleInConfig={isLocaleInConfig}
				>
					<BMEntitySelect />
				</Header>

				<Sidebar menu={menu()} />

				<main class={styles["main"]}>
					<div class="main-layout">
						<Breadcrumb />
						<Suspense fallback={<Loading soft />}>{props.children}</Suspense>
					</div>
				</main>
			</div>
		</>
	);
}
