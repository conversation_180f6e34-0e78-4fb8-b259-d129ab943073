import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary, Show } from "solid-js";
import { A, createRouteData, redirect, useNavigate, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ProductFormCreate } from "~/containers/ProductFormCreate";
import { CONTRACT_STATUS } from "~/services/tender/contract.model";
import { getLot } from "~/services/tender/lot.client";
import { getProduct, updateProduct } from "~/services/tender/product.client";
import MdiKeyboardReturn from '~icons/mdi/keyboard-return'

export function routeData({ location }) {
	return createRouteData(
		async ([query]) => {
			const code = query.code;

			if (code === "") {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

            const resProduct = await getProduct({
				code,
				option: {
                    total: true,
					contract: true,
                },
			});
			
			if (resProduct.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

            const product = resProduct.data[0];
            const lotID = product.lotID;

            const resLot = await getLot({
                lotID
            })

            if (resLot.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

			return {
				product: product,
				contract: product.contract,
				lot: resLot.data[0]
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="product:product_info"
			namespaces={["product"]}
			breadcrumbs={[BREADCRUMB.PRODUCT, BREADCRUMB.EDIT_PRODUCT]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 
    const getData = useRouteData();

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<div class="d-flex align-items-center justify-content-between">
						<A href={BREADCRUMB.PRODUCT.link}>
							<Button color="secondary" startIcon={<MdiKeyboardReturn />} >
								Quay lại
							</Button>
						</A>
						<h1 class="page-title">{t`product:product_info`}</h1>
					</div>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<Show when={getData()}>
                            <EditProduct lot={getData()?.lot} product={getData()?.product} contract={getData()?.contract}/>
                        </Show>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function EditProduct(props) {
    const { t } = useTranslate();
    const toast = useToast();
	
    const hookForm = createForm({
		initialValues: {
            lotID: props.product.lotID,
			contractID: props.product.contractID,
            price: props.product.price,
			unit: props.product.unit,
            amount: props.product.amount,
            quantity: props.product.quantity,
            productID: props.product.productID,
            productCode: props.product.productCode,
            bidID: props.lot.bidID,
            code: props.product.code,
			mainCategory: props.contract.mainCategory,
			registrationNo: props.product.registrationNo,
			groupMedicine: props.product.groupMedicine
		},
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
            
            const payload = data;
            payload.lotID = +payload.lotID;
            payload.productID = +payload.productID;
			payload.groupMedicine = +data.groupMedicine;
			payload.registrationNo = data.registrationNo;

            const res = await updateProduct(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Update product:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.update_success`);
            }

            return;
		},
	});

	return (
		<ProductFormCreate isEdit hookForm={hookForm} lot={props.lot} contract={props.contract}/>
	);
}