import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";

const URL = "/oms/order/v1";

/** Get invoice info */
export async function getInvoice({
	internalOrderID,
	option,
}: {
	internalOrderID?: number;
	option?: OrderQueryOption;
}): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/invoice`, {
		internalOrderID,
		option: option ? Object.keys(option).join(",") : undefined,
	});
}

// ===================================================
// For QUERY
// ===================================================
export type OrderQuery = {};

export type OrderQueryOption = MasterDataOption & {};
