import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";
import { QueryOption } from "./bid.model";
import { Contract } from "./contract.model";

const URL = "/tender/core-tender/v1";

/** Return a list of contract  */
export async function getContractList(
	input?: QueryInput<ContractQuery, QueryOption>
): Promise<APIResponse<Contract>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/contract/list`, {
		...input,
		search: input.search ? String(input.search) : undefined,
	});
}

/** Return a list of contract annex */
export async function getContractAnnexList(
	input?: QueryInput<ContractQuery, QueryOption>
): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/contract-annex/list`, {
		...input,
		search: input.search ? String(input.search) : undefined,
	});
}

/** Create contract */
export async function createContract(data: Contract): Promise<APIResponse<Contract>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/contract`, data);
}

/** Create contract annex */
export async function createContractAnnex(data: Contract): Promise<APIResponse<Contract>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/contract-annex`, data);
}

/** Update contract information */
export async function updateContract(data: Partial<Contract>): Promise<APIResponse<Contract>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/contract`, data);
}

/** Update contract annex information */
export async function updateContractAnnex(data: Contract): Promise<APIResponse<Contract>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/contract-annex`, data);
}

/** Get contract info */
export async function getContract({
	code,
	option,
}: {
	code?: string;
	option?: QueryOption;
}): Promise<APIResponse<Contract>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/contract`, {
		code,
		option: option ? Object.keys(option).join(",") : undefined,
	});
}

/** Get contract info */
export async function getContractAnnex({
	code,
	contractCode,
	option,
}: {
	code?: string;
	contractCode?: string;
	option?: QueryOption;
}): Promise<APIResponse<Contract>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/contract-annex`, {
		code,
		contractCode,
		option: option ? Object.keys(option).join(",") : undefined,
	});
}

// ===================================================
// For QUERY
// ===================================================
export type ContractQuery = {};

export type ContractQueryOption = MasterDataOption & {};