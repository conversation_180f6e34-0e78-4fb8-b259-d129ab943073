import { <PERSON><PERSON>, Card, Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatCurrency, formatDatetime } from "@buymed/solidjs-component/utils";
import { Index, Show } from "solid-js";
import { A } from "solid-start";
import BMTablePagination from "~/components/table/BMTablePagination";
import { ORDER_STATE, ORDER_STATE_LABEL, ORDER_STATUS_LABEL_COLOR, OrderStatusLabelColor } from "~/services/tender/order.model";
import EditIcon from "~icons/mdi/square-edit-outline";
import { OrderChangeStatusModal } from "../OrderChangeStatusModal";
import { formatEllipsis } from "~/utils/format";

export function OrderTable(props) {
    const { t } = useTranslate();
    return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>Order ID</TableHeaderCell>
						<TableHeaderCell>SO</TableHeaderCell>
						<TableHeaderCell>Hợp đồng</TableHeaderCell>
						<TableHeaderCell>Khách hàng</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								Thanh toán	
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								Tổng tiền
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								Ngày mua
							</div>
						</TableHeaderCell>
						<Show when={props.tab === 6}>
							<TableHeaderCell>
								<div style={{ "text-align": "right" }}>
									Ngày giao hàng
								</div>
							</TableHeaderCell>
						</Show>
						<TableHeaderCell>Ghi chú</TableHeaderCell>
						<TableHeaderCell class="col-2">
							<div style={{ "text-align": "center" }}>
								Trạng thái
							</div>
						</TableHeaderCell>
						<TableHeaderCell>Thao tác</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.orders}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
								Không tìm thấy dữ liệu đơn hàng
								</TableCell>
							</TableRow>
						}
					>
						{(order) => <OrderTableRow item={order()} contract={props.contractMap[order().contractCode]} tab={props.tab}/>}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

function OrderTableRow(props) {
    const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>
				<A href={`/order/${props.item.orderCode}`}>
					{props.item.orderID}
				</A>
			</TableCell>
			<TableCell>
				<A target="_blank" href={`${import.meta.env.VITE_INTERNAL_BUYMED_HOST}/wms?orderId=${props.item.orderID}`}>
					{props.item.saleOrderCode}
				</A>
			</TableCell>
			<TableCell>
				<Show when={props.contract} fallback={
					<Tooltip content={`Hợp đồng`}>
						<A href={`/contract/${props.item.contractCode}`}>
							{props.item.contractCode}
						</A>
					</Tooltip>
				}>
					<Tooltip content={`Hợp đồng`}>
						<A href={`/contract/${props.item.contractCode}`}>
							[{props.contract.contractNumber}]
							<br/>
							{props.contract.beneficiaryName}
						</A>
					</Tooltip>
				</Show>
				
			</TableCell>
			<TableCell class="col-2">
				{props.item.receiverName}
				<br/>
				ĐT: {props.item.receiverPhoneNumber}
				<br/>
				Địa chỉ: {formatEllipsis(props.item.receiverShippingAddress, 200)}
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					<Show when={props.item.paymentMethod === "PAYMENT_METHOD_NORMAL"}>
						Tiền mặt
					</Show>
					<Show when={props.item.paymentMethod === "PAYMENT_METHOD_CREDIT"}>
						Công nợ
					</Show>
					<Show when={props.item.paymentMethod === "PAYMENT_METHOD_BANK"}>
						Chuyển khoản ngân hàng
					</Show>
					<Show when={
						props.item.paymentMethod !== "PAYMENT_METHOD_CREDIT" && 
						props.item.paymentMethod !== "PAYMENT_METHOD_BANK" &&
						props.item.paymentMethod !== "PAYMENT_METHOD_NORMAL"}>
						{props.item.paymentMethod}
					</Show>
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatCurrency(props.item.totalAmount)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatDatetime(props.item.createdTime)}
				</div>
			</TableCell>
			<Show when={props.tab === 6}>
				<TableHeaderCell>
					<div style={{ "text-align": "right" }}>
					{formatDatetime(props.item.deliveredTime)}
					</div>
				</TableHeaderCell>
			</Show>
			<TableCell>
				{props.item.note}
			</TableCell>
			<TableCell>
				{/* <div class="d-flex justify-content-center align-items-center gap-1">
					<Button class="m-3" color={OrderStatusLabelColor(props.item.status)}>
						{t(ORDER_STATE_LABEL[props.item.status])}
					</Button>
				</div> */}
				<TableCell>
					<div class="d-flex justify-content-center align-items-center gap-1">
						<OrderChangeStatusModal 
							orderID={props.item.orderID}
							disabled={props.item.status !== ORDER_STATE.WAIT_TO_CONFIRM}
							color={OrderStatusLabelColor(props.item.status)} 
							buttonText={t(ORDER_STATE_LABEL[props.item.status])}
						/>
					</div>
				</TableCell>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content="Xem chi tiết đơn hàng">
						<Button
							class="p-2"
							variant="outline"
							color="primary"
							href={`/order/${props.item.orderCode}`}
							startIcon={<EditIcon />}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}