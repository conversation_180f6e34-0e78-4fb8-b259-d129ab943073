
export const ORDER_STATE = {
    WAIT_TO_CONFIRM: "WAIT_TO_CONFIRM",
	CONFIRMED: "CONFIRMED",
    PROCESSING: "PROCESSING",
    WAIT_TO_DELIVER: "WAIT_TO_DELIVER",
    DELIVERING: "DELIVERING",
    DELIVERED: "DELIVERED",
    COMPLETED: "COMPLETED",
    CANCELLED: "CANCELLED",
} as const;

export const ORDER_STATUS_LABEL_COLOR = {
    [ORDER_STATE.CONFIRMED]: "info",
    [ORDER_STATE.PROCESSING]: "primary",
    [ORDER_STATE.WAIT_TO_DELIVER]: "primary",
    [ORDER_STATE.DELIVERING]: "primary",
    [ORDER_STATE.DELIVERED]: "primary",
	[ORDER_STATE.COMPLETED]: "success",
    [ORDER_STATE.CANCELLED]: "danger",
} as const;

export function OrderStatusLabelColor(value) : string {
    if(typeof(ORDER_STATUS_LABEL_COLOR[value]) != "undefined") {
        return ORDER_STATUS_LABEL_COLOR[value]
    }
    return "warning";
}

export const ORDER_STATE_LABEL = {
	[ORDER_STATE.WAIT_TO_CONFIRM]: "order:state.WAIT_TO_CONFIRM",
    [ORDER_STATE.CONFIRMED]: "order:state.CONFIRMED",
    [ORDER_STATE.PROCESSING]: "order:state.PROCESSING",
    [ORDER_STATE.WAIT_TO_DELIVER]: "order:state.WAIT_TO_DELIVER",
    [ORDER_STATE.DELIVERING]: "order:state.DELIVERING",
    [ORDER_STATE.DELIVERED]: "order:state.DELIVERED",
    [ORDER_STATE.COMPLETED]: "order:state.COMPLETED",
    [ORDER_STATE.CANCELLED]: "order:state.CANCELLED",
} as const;

export const INVOICE_STATUS_LABEL_COLOR = {
    ["PROCESSED"]: "primary",
    ["COMPLETED"]: "success",
}
export function InvoiceStatusLabelColor(value) : string {
    if(typeof(INVOICE_STATUS_LABEL_COLOR[value]) != "undefined") {
        return INVOICE_STATUS_LABEL_COLOR[value]
    }
    return "info";
}

// ===================================================
// Validation
// ===================================================
export async function validateOrderForm(values, t) {
	const err: any = {};

    if (!values.receiverName || values.receiverName.length == 0) {
        err.receiverName = t`order:receiver_name_required`;
    }

    if (!values.phoneNumber || values.phoneNumber.length == 0) {
        err.phoneNumber = t`order:phone_number_required`;
    }

    if (!values.provinceCode || values.provinceCode.length == 0) {
        err.provinceCode = t`order:province_code_required`;
    }

    if (!values.districtCode || values.districtCode.length == 0) {
        err.districtCode = t`order:district_code_required`;
    }

    if (!values.wardCode || values.wardCode.length == 0) {
        err.wardCode = t`order:ward_code_required`;
    }

    if (!values.shippingAddress || values.shippingAddress.length == 0) {
        err.shippingAddress = t`order:shipping_address_required`;
    }
    
	return err;
}