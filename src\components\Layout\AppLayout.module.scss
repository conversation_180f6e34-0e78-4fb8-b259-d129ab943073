.layout {
	display: flex;

	transition: margin-left var(--animation-speed);
}
.layout.sidebar-open .main {
	display: flex;
	margin-left: var(--sidebar-open-width);
	width: calc(100% - var(--sidebar-open-width));
}

.main {
	margin: var(--header-height) auto 0;
	margin-left: var(--sidebar-width);
	width: calc(100% - var(--sidebar-width));
	padding-top: 0;

	/* TODO: For mobile, use smaller padding */
}

@media screen and (max-width: 480px) {
	.layout,
	.layout.sidebar-open .main,
	.layout .main {
		margin-left: 0;
		width: 100%
	}
}
