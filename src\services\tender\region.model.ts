export const REGION_OPTIONS = (t) => [
    { value: "NORTH", label: "Miền Bắc" },
    { value: "CENTER", label: "Miền Trung" },
    { value: "SOUTH", label: "Miền Nam" },
    { value: "HCM", label: "<PERSON>hu vực HCM" },
];

export const REGION_LABEL = {
    "NORTH": "Miền Bắc",
    "CENTER": "Miền Trung",
    "SOUTH": "Miền Nam",
    "HCM": "Khu vực HCM"
};

export const YEAR_OPTIONS = () => {
    const options = [];
    const maxYear = (new Date().getFullYear()) + 10;

    for (let i = 2020; i <= maxYear; i++) {
        options.push({ value: i, label: `${i}` });
    }
    return options;
};