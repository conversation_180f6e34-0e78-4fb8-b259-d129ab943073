import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary } from "solid-js";
import { useNavigate } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { BenaficiaryForm } from "~/containers/Benaficiary/form";
import { FormProvider } from "~/contexts/FormContext";
import { postBeneficiary } from "~/services/tender/beneficiary.client";
import { BENEFICIARY_STATUS, validateBeneficiaryForm } from "~/services/tender/beneficiary.model";
import { ERROR_MESSAGE } from "~/utils/error-code";

export default () => {
	return (
		<AppLayout
			pageTitle="beneficiary:add_new"
			namespaces={["beneficiary"]}
			breadcrumbs={[BREADCRUMB.BENEFICIARY, BREADCRUMB.ADD_BENEFICIARY]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Thêm mới khách hàng</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddBeneficiary />
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddBeneficiary() {
	const { t } = useTranslate();
	const toast = useToast();
	const navigate = useNavigate();
	const initialValues = {
		name: "",
		address: "",
		phoneNumber: "",
		taxCode: "",
		status: BENEFICIARY_STATUS.ACTIVE,
		email: "",
	};
	const hookForm = createForm({
		initialValues,
		validate: (values) => {
			return validateBeneficiaryForm(values);
		},
		onSubmit: async (values) => {
			try {
				const res = await postBeneficiary({
					...values,
					status: values.status ? BENEFICIARY_STATUS.ACTIVE : BENEFICIARY_STATUS.INACTIVE,
				});
				if (res.status !== API_STATUS.OK) {
					const errorCode = res?.errorCode;
					const message = ERROR_MESSAGE?.[errorCode] || "";
					return toast.error(
						message
							? message
							: t("common:notify.action_fail", { error: "Vui lòng kiểm tra lại" })
					);
				}
				toast.success(t`common:notify.create_success`);
				navigate("/beneficiary/new");
			} catch (error) {
				console.error("[Error] Create beneficiary:", error);
				toast.error(t("common:notify.action_fail", { error: error.message }));
			}
		},
	});

	return (
		<FormProvider form={hookForm}>
			<BenaficiaryForm />
		</FormProvider>
	);
}
