# Project tender (bidding)



##

```
server { 
  listen 4000; 
  server_name local.buymed.tech www.local.buymed.tech; 
  client_max_body_size 7M; 
  # access_log /var/log/nginx/access.log upstream_time; 
  # error_log /var/log/nginx/error.log warn; 
 
  add_header 'instance' 'local'; 
 
  location /backend { 
      proxy_pass https://sso.stg.buymed.tech/backend; 
      if ($request_method != OPTIONS) { 
        add_header Access-Control-Allow-Origin *; 
      } 
      proxy_set_header Host "sso.stg.buymed.tech"; 
      proxy_set_header Upgrade $http_upgrade; 
      proxy_set_header Connection "upgrade"; 
      proxy_connect_timeout 10s; 
      proxy_read_timeout 120s; 
  } 
 
  location / { 
      # khi yarn dev -> web sẽ chạy port 3000, nginx sẽ proxy request user tới port 3000 
      proxy_pass http://localhost:3000; 
      proxy_http_version 1.1; 
      proxy_set_header Upgrade $http_upgrade; 
      proxy_set_header Connection 'upgrade'; 
      proxy_set_header Host $host; 
      proxy_cache_bypass $http_upgrade; 
  } 
}
```
//

+ <PERSON><PERSON><PERSON> muốn thêm thông tin mời thầu (IB)
+ <PERSON><PERSON><PERSON> muốn cập nhật thông tin mời thầu:
   - Thông tin/trạng thái
   - Tình trạng đấu thầu của các SKU
+ Xem các thầu đang còn/hết thời hạn đấu 
   - Lọc các thầu hết thời hạn nhưng chưa cập nhật các SKU liên quan
+ Thêm Hợp đồng cho 1 thầu
+ Kiểm tra 1 sản phẩm:
   - Đang ký với các cơ sở y tế nào
   - Số lượng total bao nhiêu, đã mua & còn lại bao nhiêu
+ Liệt kê các hóa đơn gần đến hạn thanh toán (trước 15 ngày đến hạn) theo công nợ.
