import MdiInvoice from '~icons/mdi/invoice';
import <PERSON>di<PERSON><PERSON>mer from '~icons/mdi/hammer'
import Mdi<PERSON>eorderHorizontal from '~icons/mdi/reorder-horizontal';
import MdiStickerTextOutline from '~icons/mdi/sticker-text-outline';
import MdiAccount from '~icons/mdi/account';
import { ROUTES } from "./breadcrumb";

/**
 * @typedef Menu
 * @property {string=} key
 * @property {string} name - The i18n key, usually in locales/[lang]/common.json
 * @property {string} link
 * @property {import("solid-js").ValidComponent=} icon
 * @property {Menu[]=} subMenu
 */

/** @type {Menu[]} */
/** Icon: https://icones.netlify.app/collection/mdi */
export const MENU = [
	{
		name: "common:sidebar.bid",
		link: ROUTES.BID,
		icon: MdiHammer,
	},
	{
		name: "common:sidebar.contract",
		link: ROUTES.CONTRACT,
		icon: MdiInvoice,
	},
	{
		name: "common:sidebar.product",
		link: ROUTES.PRODUCT,
		icon: MdiStickerTextOutline,
	},
	{
		name: "common:sidebar.beneficiary",
		link: ROUTES.BENEFICIARY,
		icon: MdiAccount,
	},
	{
		name: "common:sidebar.order",
		link: ROUTES.ORDER,
		icon: MdiReorderHorizontal,
	},
	// {
	// 	name: "common:sidebar.invoice",
	// 	link: ROUTES.INVOICE,
	// 	icon: MdiInvoice,
	// },
];
