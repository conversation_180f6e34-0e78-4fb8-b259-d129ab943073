import { A } from "@solidjs/router";
import { NotFound, useTranslate } from "@buymed/solidjs-component/components";
import { HttpStatusCode } from "solid-start/server";
import AppLayout from "~/components/Layout/AppLayout";

export default () => {
	return (
		<AppLayout pageTitle="404:pageTitle" namespaces={["404"]}>
			<HttpStatusCode code={404} />
			<NotFoundPage />
		</AppLayout>
	);
};

function NotFoundPage() {
	const { t } = useTranslate();

	return (
		<NotFound
			title={t`404:title`}
			subTitle={t`404:subTitle`}
			actionContent={
				<>
					{t`404:actionContent`}{" "}
					<A href="/" class="text-success">
						{t`404:returnPageTitle`}
					</A>
				</>
			}
		/>
	);
}
