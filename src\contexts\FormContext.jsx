// contexts/FormContext.tsx
import { createContext, useContext } from "solid-js";

const FormContext = createContext();

export const FormProvider = (props) => {
	return <FormContext.Provider value={props.form}>{props.children}</FormContext.Provider>;
};

export const useForm = () => {
	const context = useContext(FormContext);
	if (!context) {
		throw new Error("useForm must be used within a FormProvider");
	}
	return context;
};
