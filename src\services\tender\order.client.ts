import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { Lot } from "./lot.model";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { QueryOption } from "./bid.model";

const URL = "/tender/core-tender/v1";

/** Return a list of lot  */
export async function getOrderList(
	input?: QueryInput<OrderQuery, QueryOption>
): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/order/list`, {
		...input,
		search: input.search ? String(input.search) : undefined,
	});
}


/** Get order info */
export async function getOrder({
	code,
	option,
}: {
	code?: string;
	option?: OrderQueryOption;
}): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/order`, {
		code,
		option: option ? Object.keys(option).join(",") : undefined,
	});
}


/** Create order */
export async function createOrder(data: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/order`, data);
}

/** Update order status */
export async function updateOrderStatus(data: Partial<any>): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/order/status`, data);
}

export async function cancelOrder(data: Partial<any>): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/cancel-order`, data);
}

export async function requestInvoiceExchange(data: Partial<any>): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/invoice-exchange`, data);
}

// ===================================================
// For QUERY
// ===================================================
export type OrderQuery = {};

export type OrderQueryOption = MasterDataOption & {
	contract?: boolean;
	bid?: boolean;
};