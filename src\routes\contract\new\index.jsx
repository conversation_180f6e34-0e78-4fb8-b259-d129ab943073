import {
    Col,
    ErrorMessage,
    Row,
    useToast,
    useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary } from "solid-js";
import { useNavigate } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ContractFormCreate } from "~/containers/ContractFormCreate";
import { createContract } from "~/services/tender/contract.client";
import { CONTRACT_TYPE, validateContractForm } from "~/services/tender/contract.model";
import { LEGAL_ENTITY_BANK_OPTIONS, LEGAL_ENTITY_OPTIONS } from "~/services/tender/legal.model";

export default () => {
	return (
		<AppLayout
			pageTitle="contract:add_contract"
			namespaces={["contract"]}
			breadcrumbs={[BREADCRUMB.CONTRACT, BREADCRUMB.ADD_CONTRACT]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Tạo hợp đồng</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddContract/>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddContract() {
    const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();
	
    const hookForm = createForm({
		initialValues: {
            bidID: null,
            contractNumber: "",
            beneficiaryName: "",
            address: "",
            name: "",
            phoneNumber: "",
            taxCode: "",
            contractType: CONTRACT_TYPE.DEBT,
            debtLimit: 0,
            paymentTerm: 90,
            startTime: null,
            endTime: null,
            signingDate: null,
            expireDate: null,
            attachments: [""],
            extendAttachments: [""],
            products: [{}],
            isStoreContractDocument: false,
            isIndividual: false,
            legalEntityCode: LEGAL_ENTITY_OPTIONS[0].value,
            legalEntityName: LEGAL_ENTITY_OPTIONS[0].data.name,
            legalEntityTaxCode: LEGAL_ENTITY_OPTIONS[0].data.taxCode,
            legalEntityAddress: LEGAL_ENTITY_OPTIONS[0].data.address,
            legalEntityEmail: LEGAL_ENTITY_OPTIONS[0].data.email,
            legalEntityTel: LEGAL_ENTITY_OPTIONS[0].data.tel,
		},
		validate: (values) => validateContractForm(values,false, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
            const payload = data;
            payload.bidID = +payload.bidID;
            payload.attachments = payload.attachments.filter((e) => e.length > 0);
            payload.extendAttachments = payload.extendAttachments.filter((e) => e.length > 0);
            payload.products.forEach((e) => {
                e.productID = +e.productID;
                e.lotID = +e.lotID;
                e.vat = +e.vat;
            })
            payload.legalEntityBranch = LEGAL_ENTITY_OPTIONS.find((e) => e.value == payload.legalEntityCode).data?.legalEntityBranch ?? "";

            console.log("legalEntityBankCode=",payload.legalEntityBankCode);
            LEGAL_ENTITY_BANK_OPTIONS.forEach((e) => {
                if(e.data.bankCode == payload.legalEntityBankCode) {
                    payload.legalEntityBank = e.data
                }
            })
            console.log(payload);
            const res = await createContract(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create contract:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.create_success`);
			    navigate("/contract", { replace: false });
            }

            return;
		},
	});

	return (
		<ContractFormCreate hookForm={hookForm}/>
	);
}