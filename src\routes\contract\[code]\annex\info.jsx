import {
    Col,
    ErrorMessage,
    Row,
    useToast,
    useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary, Show } from "solid-js";
import { createRouteData, redirect, refetchRouteData, useNavigate, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ContractAnnexFormCreate } from "~/containers/ContractAnnexFormCreate";
import { getContract, getContractAnnex, updateContractAnnex } from "~/services/tender/contract.client";
import { validateContractAnnexForm, validateContractForm } from "~/services/tender/contract.model";


export function routeData({ location, params }) {
    return createRouteData(
		async ([query]) => {
            const contractCode = params.code;
            const annexCode = query.code;

			if (contractCode === "" || annexCode == "") {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

            const [respContract, respAnnex] = await Promise.all([
				getContract({
					code: contractCode,
					option: {},
				}),
				getContractAnnex({
                    code: annexCode,
                    contractCode: contractCode,
                    option: {},
                })
			]);

			if (respContract.status !== API_STATUS.OK || respAnnex.status !== API_STATUS.OK) {
				throw redirect("/404");
			}
            return {
                annex: respAnnex.data[0],
                contract: respContract.data[0]
            };
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
    const param = useParams();
	return (
		<AppLayout
			pageTitle="contract:update_contract"
			namespaces={["contract"]}
			breadcrumbs={[
                BREADCRUMB.CONTRACT, 
                {
                    label: "Danh sách phụ lục",
                    link: `/contract/${param.code}`,
                },
                {
                    label: "Thông tin phụ lục",
                    link: `/contract/${param.code}`,
                },
            ]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 
    const getData = useRouteData();

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Chỉnh sửa phụ lục hợp đồng</h1>
				</Col>
				<Col xs={12}>
                    <Show when={getData()}>
                        <ErrorBoundary fallback={ErrorMessage}>
                            <EditContractAnnex annex={getData()?.annex} contract={getData()?.contract}/>
                        </ErrorBoundary>
                    </Show>
				</Col>
			</Row>
		</div>
	);
}

function EditContractAnnex(props) {
    const { t } = useTranslate();
    const toast = useToast();
	
    const hookForm = createForm({
		initialValues: {
            annexNumber: props.annex.annexNumber,
            name: props.annex.name,
            content: props.annex.content,
            contractDate: props.annex.contractDate,
            expireDate: props.annex.expireDate,
            attachments: props.annex.attachments ? props.annex.attachments.concat("") : [""],
		},
		validate: (values) => validateContractAnnexForm(values, true, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
            const payload = data;

            payload.attachments = payload.attachments.filter((e) => e.length > 0);
            payload.code = props.annex.code;

            if(!payload.contractDate) {
				delete payload.contractDate;
			}

			if(!payload.expireDate) {
				delete payload.expireDate;
			}
            
            const res = await updateContractAnnex(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Update contract:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.update_success`);
                refetchRouteData();
            }

            return;
		},
	});

	return (
		<ContractAnnexFormCreate 
            hookForm={hookForm} 
            contract={props.contract}
            isEdit={true}
            contractOptions={[{label: props.contract.name, value: props.contract.code}]}/> 
	);
}