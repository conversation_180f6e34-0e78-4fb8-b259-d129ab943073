// @refresh reload
import {
	ToastProvider,
	handleImportModuleError,
	registerHandlers,
} from "@buymed/solidjs-component/components";
import "base-fe/dist/css/bootstrap.min.css";
import hasOwn from "object.hasown";
import { ErrorBoundary, Suspense } from "solid-js";
import { Body, FileRoutes, Head, Html, Link, Meta, Routes, Scripts } from "solid-start";
import AuthProvider from "./contexts/AuthContext";
import BreadcrumbProvider from "./contexts/BreadcrumbContext";
import "./root.scss";

// Polyfill hasOwn function for old browers: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/hasOwn
if (!Object.hasOwn) {
	Object.hasOwn = hasOwn;
}

export default function Root() {
	return (
		<Html lang="en">
			<Head>
				<Meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
				<Meta name="viewport" content="width=device-width, initial-scale=1" />

				<Link rel="preconnect" href="https://fonts.gstatic.com" />
				<Link
					rel="stylesheet"
					href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
				/>

				{/* Disable cache - so when there is a new deploy, the page get the latest assets */}
				<Meta http-equiv="Cache-control" content="no-cache, no-store, must-revalidate" />
				<Meta http-equiv="Pragma" content="no-cache" />

				{/* Favicon generated with https://realfavicongenerator.net/favicon_result?file_id=p1h19053m41e6b1ikj10nc13j716vr6 */}
				<Link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png" />
				<Link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon-32x32.png" />
				<Link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png" />
				<Link rel="icon" type="image/svg+xml" href="/favicon/buymed.svg" />
				<Link rel="mask-icon" href="/favicon/safari-pinned-tab.svg" color="#5bbad5" />
				<Meta name="theme-color" content="#ffffff" />
			</Head>
			<Body>
				<Suspense>
					<ErrorBoundary fallback={registerHandlers([handleImportModuleError])}>
						<AuthProvider>
							<BreadcrumbProvider>
								<ToastProvider>
									<Routes>
										<FileRoutes />
									</Routes>
								</ToastProvider>
							</BreadcrumbProvider>
						</AuthProvider>
					</ErrorBoundary>
				</Suspense>
				<Scripts />
			</Body>
		</Html>
	);
}
