import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";
import { QueryOption } from "./bid.model";

const URL = "/tender/core-tender/v1";

/** Return a list of beneficiary  */
export async function getBeneficiaryList(
	input?: QueryInput<BeneficiaryQuery, QueryOption>
): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/beneficiary/list`, {
		...input,
		search: input.search ? String(input.search) : undefined,
	});
}

export async function postBeneficiary({ ...props }) {
	return callAPI(HTTP_METHOD.POST, `${URL}/beneficiary`, {
		...props,
	});
}

export async function putBeneficiary({ ...props }) {
	return callAPI(HTTP_METHOD.PUT, `${URL}/beneficiary`, {
		...props,
	});
}

// ===================================================
// For QUERY
// ===================================================
export type BeneficiaryQuery = {};

export type BeneficiaryQueryOption = MasterDataOption & {};
