import { <PERSON><PERSON>, Col, DEFAULT_LIMIT, DEFAULT_PAGE, Row, useTranslate } from "@buymed/solidjs-component/components";
import { toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, createResource } from "solid-js";
import { ErrorMessage, createRouteData, useRouteData, useSearchParams } from "solid-start";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ContractFormFilter } from "~/containers/ContractFormFilter";
import { ContractTable } from "~/containers/ContractTable";
import { getBidList } from "~/services/tender/bid.client";
import { getContractList } from "~/services/tender/contract.client";
import { CONTRACT_STATUS } from "~/services/tender/contract.model";
import AddIcon from "~icons/mdi/plus";

export function routeData({ location }) {
    return createRouteData(
		async ([query]) => {
			const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			// Prepare query data

			// TODO: Fix <DateRangePicker> to allow customizing name, so we can use createdFrom, not createdTimeStartDate
			if (q.createdTimeStartDate) {
				q.createdFrom = q.createdTimeStartDate;
			}
			if (q.createdTimeEndDate) {
				q.createdTo = q.createdTimeEndDate;
			}

			switch (query.tab) {
				case "1": {
					q.status = CONTRACT_STATUS.ACTIVE;
					break;
				}
				case "2": {
					q.status = CONTRACT_STATUS.DONE
					break;
				}
				case "3": {
					q.status = CONTRACT_STATUS.FINISH
					break;
				}
				case "4": {
					q.status = CONTRACT_STATUS.DRAFT
					break;
				}
			}

			const resContract = await getContractList({
				q,
				offset,
				limit,
				option: {
					total: true,
				},
			});

			let bidIDs = [];
			(resContract.data || [])?.forEach((contract) => {
				bidIDs = bidIDs.concat(contract.bidID || []);
			});
			bidIDs = Array.from(new Set(bidIDs));
			const bidMap = {};

			if (bidIDs.length > 0) {
				const resBidList = await getBidList({
					q: {bidIDs},
					offset: 0,
					limit: 1000,
					option: {}
				});

				(resBidList.data || []).forEach(
					(item) => (bidMap[item.bidID] = item)
				);
			}

			return {
				data: resContract.data,
				total: resContract.total,
				bidMap,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout namespaces={["contract"]} pageTitle="contract:contract_list" breadcrumbs={[BREADCRUMB.CONTRACT]}>
			<PageContainer />
		</AppLayout>
	);
};


function PageContainer() {
	const pageData = useRouteData();
	const [searchParams] = useSearchParams();
	const { t } = useTranslate();

	const [tabs] = createResource(
		// Fetch the count for tabs
		// We only need re-fetch when q change (i.e. submit filter), when tab change
		// no need to re-fetch
		() => toQueryObject(searchParams).q || "{}",
		async (qString) => {
			const q = JSON.parse(qString);
			
			if (q.createdTimeStartDate) {
				q.createdFrom = q.createdTimeStartDate;
			}
			if (q.createdTimeEndDate) {
				q.createdTo = q.createdTimeEndDate;
			}

			const totalRes = await Promise.all([
				getContractList({
					q,
					limit: 1,
					option: { total: true },
				}),
				getContractList({
					q: { ...q, status: CONTRACT_STATUS.ACTIVE },
					limit: 1,
					option: { total: true },
				}),
				getContractList({
					q: { ...q, status: CONTRACT_STATUS.DONE },
					limit: 1,
					option: { total: true },
				}),
				getContractList({
					q: { ...q, status: CONTRACT_STATUS.FINISH },
					limit: 1,
					option: { total: true },
				}),
                getContractList({
					q: { ...q, status: CONTRACT_STATUS.DRAFT },
					limit: 1,
					option: { total: true },
				}),
			]);
			const totals = totalRes.map((res) => res.total || 0);

			return [
				t`contract:status.all` + ` (${totals[0]})`,
				t`contract:status.ACTIVE` + ` (${totals[1]})`,
				t`contract:status.DONE` + ` (${totals[2]})`,
				t`contract:status.FINISH` + ` (${totals[3]})`,
				t`contract:status.DRAFT` + ` (${totals[4]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<div class="d-flex justify-content-start">
					<h1 class="page-title">{t`contract:contract_list`}</h1>
				</div>
				<div class="d-flex justify-content-end">
					<AuthContent privilege={PRIVILEGE.CONTRACT.CREATE} >
						<Button
							color="success"
							href="/contract/new"
							style={{"margin-right": "5px"}}
							startIcon={<AddIcon class="fs-4" />}
						>
							{t`contract:add_contract`}
						</Button>
					</AuthContent>
					{/* <AuthContent privilege={PRIVILEGE.CONTRACT.CREATE} >
						<Button
							color="success"
							href="/contract/individual-new"
							startIcon={<AddIcon class="fs-4" />}
						>
							{t`contract:add_contract_individual`}
						</Button>
					</AuthContent> */}
				</div>
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<ContractFormFilter />
				</ErrorBoundary>
			</Col>
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				{/* <ErrorBoundary fallback={ErrorMessage}> */}
					<ContractTable 
						contracts={pageData()?.data} 
						total={pageData()?.total} 
						bidMap={pageData()?.bidMap}
					/>
				{/* </ErrorBoundary> */}
			</Col>
		</Row>
	);
}