import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary } from "solid-js";
import { useNavigate } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { OrderFormCreate } from "~/containers/OrderFormCreate";
import { createOrder } from "~/services/tender/order.client";
import { validateOrderForm } from "~/services/tender/order.model";

export default () => {
	return (
		<AppLayout
			pageTitle="order:add_order"
			namespaces={["order"]}
			breadcrumbs={[BREADCRUMB.ORDER, BREADCRUMB.ADD_ORDER]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Tạo đơn hàng</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddOrder/>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddOrder() {
    const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();
	
    const hookForm = createForm({
		initialValues: {
			products: [],
			canExportInvoice: true,
		},
		validate: (values) => validateOrderForm(values, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
			
            const payload = data;
			payload.contractCode = payload.contract.code;
			payload.products.forEach((e) => {
                e.productID = +e.productID;
            })
			payload.products = payload.products.filter((e) => e.quantity > 0);
			payload.regionCode="-";
            console.log(payload);

			let totalAmount = 0;
			payload.products.forEach((e) => totalAmount = totalAmount + e.amount);

			if(totalAmount <= 0) {
				toast.error(t("common:notify.action_fail", { error: 'Đơn hàng đặt phải có tổng tiền lớn hơn 0' }));
				return
			}

			if(!payload.totalBalanceTemporary) {
				toast.error(t("common:notify.action_fail", { error: 'Đơn hàng có công nợ không xác định' }));
				return;
			}

			if(payload.totalBalanceTemporary-totalAmount < 0) {
				toast.error(t("common:notify.action_fail", { error: 'Đơn hàng đặt vượt mức công nợ cho phép' }));
				return;
			}
			
            const res = await createOrder(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create product:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:create_success`);
			    navigate("/order", { replace: false });
            }

            return;
		},
	});

	return (
		<OrderFormCreate hookForm={hookForm}/>
	);
}