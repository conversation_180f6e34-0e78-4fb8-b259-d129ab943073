import { APIResponse } from "@buymed/solidjs-component/services";
import { HTTP_METHOD, getSessionToken } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";
import { GRANT_TYPE, OAuthTokenOutput } from "./iam.model";
import {
	ActionSource,
	LoginSessionInfo,
} from "@buymed/solidjs-component/src/services/iam/account.model";
import { Entity, GetEntityList } from "@buymed/solidjs-component/src/services/iam/entity.model";

const URL = "/iam/core/v1";

// ==================
// /account
// ==================
export async function getMe(
	uid = 0,
	{ getEntities = false, getPermission = false }
): Promise<APIResponse<ActionSource>> {
	return callAPI(
		HTTP_METHOD.GET,
		`${URL}/account/me`,
		{ getEntities, getPermission },
		{
			headers: { "Auth-User": String(uid) },
		}
	);
}

export async function getLoggedInAccounts(): Promise<APIResponse<LoginSessionInfo>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/account/logged-in`);
}

// ==================
// /entity
// ==================

/** Return a list of entities */
export async function getEntityList(input: GetEntityList): Promise<APIResponse<Entity>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/entity/list`, input);
}

// ==================
// /oauth
// ==================

/** Switch to another entity. E.g "BUYMED - VN" -> "BUYMED - KH" */
export async function switchEntity(entityID: number) {
	return callAPI(HTTP_METHOD.PUT, `${URL}/oauth/token`, { entityID });
}

export async function getAccessTokenByCode(
	request: Request,
	code: string
): Promise<APIResponse<OAuthTokenOutput>> {
	// TODO: For now, cannot use getRequest(). It will be fixed in the new version of  SolidStart
	let authorization;
	let userAgent;
	const reqHeaders = request.headers;

	const token = getSessionToken(reqHeaders);
	if (token) {
		authorization = `Bearer ${token}`;
	}
	userAgent = reqHeaders?.get("user-agent") ?? "";

	return callAPI(
		HTTP_METHOD.POST,
		`${URL}/oauth/token`,
		{
			code,
			clientID: import.meta.env.VITE_APP_CLIENT_ID,
			grantType: GRANT_TYPE.AuthorizationCode,
			clientSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
		},
		{
			headers: { Authorization: authorization, "User-Agent": userAgent },
		}
	);
}

export async function getAccessTokenByRefresh(
	request: Request,
	refreshToken: string
): Promise<APIResponse<OAuthTokenOutput>> {
	// TODO: For now, cannot use getRequest(). It will be fixed in the new version of  SolidStart
	let authorization;
	let userAgent;
	const reqHeaders = request.headers;

	const token = getSessionToken(reqHeaders);
	if (token) {
		authorization = `Bearer ${token}`;
	}
	userAgent = reqHeaders?.get("user-agent") ?? "";

	return callAPI(
		HTTP_METHOD.POST,
		`${URL}/oauth/token`,
		{
			refreshToken,
			clientID: import.meta.env.VITE_APP_CLIENT_ID,
			grantType: GRANT_TYPE.RefreshToken,
			clientSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
		},
		{
			headers: { Authorization: authorization, "User-Agent": userAgent },
		}
	);
}

export async function postAuthorizeFromApp(): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/oauth/authorize/from-app`, {
		responseType: "token",
		clientID: import.meta.env.VITE_APP_DOCUCMENT_CLIENT_ID,
		redirectUri: import.meta.env.VITE_FILE_DOCUMENT_PREVIEW_HOST,
	});
}