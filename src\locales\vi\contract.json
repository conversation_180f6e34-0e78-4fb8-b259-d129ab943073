{"add_contract": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> đồng", "add_contract_individual": "<PERSON><PERSON><PERSON><PERSON> hợp đồng cá nhân", "contract_list": "<PERSON><PERSON> s<PERSON><PERSON> hợp đồng", "contract_number": "<PERSON><PERSON> hợp đồng", "no": "STT", "beneficiary_name": "Đơn vị thụ hưởng", "bid": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "start_time": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "end_time": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "contract_status": "<PERSON><PERSON><PERSON><PERSON> thái", "invitation_of_bid": "<PERSON><PERSON> gói thầu", "enter_invitation_of_bid": "<PERSON><PERSON><PERSON><PERSON> mã gói thầu", "enter_contract_number": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "created_time": "<PERSON><PERSON>y t<PERSON>o HD", "signing_date": "<PERSON><PERSON><PERSON><PERSON> gian ký", "expire_date": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "list_product": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "add_product": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "phone_number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "term_of_payment": "", "num_of_product": "Số sản phẩm", "contract_value": "<PERSON><PERSON><PERSON> trị <PERSON>", "payment_to_date": "Số tiền đã thanh toán", "procuring_entity": "Đơn vị mời thầu", "view_list_contract_annex": "<PERSON><PERSON> danh s<PERSON>ch phụ lục <PERSON>", "contract_annex_list": "<PERSON><PERSON> lục hợp đồng", "add_contract_annex": "<PERSON><PERSON><PERSON><PERSON> phụ lục hợp đồng", "type": {"DEBT": "<PERSON><PERSON><PERSON> nợ", "ANNEX": "<PERSON><PERSON> lục hợp đồng"}, "status": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "DRAFT": "Nháp", "ACTIVE": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "FINISH": "<PERSON><PERSON><PERSON>", "DONE": "Đ<PERSON>h lý"}, "placeholder": {"under_bid": "<PERSON><PERSON><PERSON> kiếm gói thầu"}, "label": {"under_bid": "<PERSON><PERSON><PERSON>"}, "product": {"id": "<PERSON><PERSON> sản phẩm", "code": "Code", "name": "<PERSON><PERSON><PERSON>", "enter_name": "<PERSON><PERSON><PERSON><PERSON> tên thu<PERSON>c", "ingredient": "<PERSON><PERSON><PERSON> ch<PERSON>", "ingredient_content": "<PERSON><PERSON><PERSON>", "dosage_form": "<PERSON><PERSON><PERSON> b<PERSON>o chế", "packaging": "<PERSON>ui cách đóng gói", "registration_number": "SĐK hoặc GPNK", "vendor": "<PERSON><PERSON><PERSON> cung cấp", "manufacturer": "<PERSON><PERSON><PERSON> sản xu<PERSON>t", "unit": "Đơn vị t<PERSON>h", "group_medicine": "Nhóm", "number_bidder": "<PERSON><PERSON> l<PERSON> thầu", "lot_final_price": "<PERSON><PERSON><PERSON> dự thầu", "lot_total_price": "<PERSON><PERSON><PERSON><PERSON> tiền", "bid_guarantee_amount": "<PERSON><PERSON><PERSON> trị đảm bảo dự thầu", "status": "Status", "quantity_sold": "SL bán ra", "quantity_remaining": "SL còn lại", "quantity_processing": "SL đang xử lý", "note": "<PERSON><PERSON><PERSON>", "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu sản phẩm", "lot_no": "Số lô", "expire_date": "<PERSON><PERSON><PERSON> d<PERSON>", "view_product": "<PERSON><PERSON> thông tin sản phẩm"}, "annex": {"no": "STT", "name": "<PERSON><PERSON><PERSON> ph<PERSON> lục", "annex_number": "Số phụ lục HĐ", "open_date": "<PERSON><PERSON><PERSON> ph<PERSON> lục", "expire_date": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "content": "<PERSON><PERSON>i dung", "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu phụ lục hợp đồng"}, "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu hợp đồng", "contract_is_required": "<PERSON><PERSON> lòng nhập mã hợp đồng", "name_is_required": "<PERSON><PERSON> lòng nhập tên hợp đồng", "beneficiary_name_is_required": "<PERSON><PERSON> lòng nhập tên đơn vị thụ hưởng", "address_is_required": "<PERSON><PERSON> lòng nhập địa chỉ", "phone_number_is_required": "<PERSON><PERSON> lòng nhập số điện thoại", "tax_is_required": "<PERSON><PERSON> lòng nhập mã số thuế", "contract_value_is_greater_than_zero_less_than_1e11": "Giá trị HD phải lớn hơn 0", "expire_date_is_required": "<PERSON><PERSON> lòng nh<PERSON>p ng<PERSON><PERSON> hết hạn <PERSON>", "end_date_is_greater_than_start_date": "<PERSON><PERSON> lòng nhập thời gian kết thúc lớn hơn thời gian bắt đầu", "start_time_is_required": "<PERSON><PERSON> lòng nhập thời gian b<PERSON>t đầu", "end_time_is_required": "<PERSON><PERSON> lòng nhập thời gian kết thúc", "edit_contract": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hợp đồng", "view_list_product": "<PERSON><PERSON> danh s<PERSON>ch sản ph<PERSON>m", "contract_time_is_required": "<PERSON><PERSON> lòng nhập thời gian phụ lục", "content_is_required": "<PERSON><PERSON> lòng nh<PERSON>p nội dung", "bid_id_is_required": "<PERSON><PERSON> lòng chọn gói thầu", "debt_limit_is_required": "<PERSON><PERSON> lòng nhập gi<PERSON>i hạn công nợ", "pay_term_is_required": "<PERSON><PERSON> lòng nhập kì hạn thanh toán"}