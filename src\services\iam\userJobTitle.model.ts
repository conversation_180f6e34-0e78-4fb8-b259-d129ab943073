import { Department } from "../department/department.model";

export interface UserJobTitle {
	lastUpdatedTime: string;
	createdTime: string;

	orgID: number;
	accountID: number;
	departmentCode: string;
	departmentName: string;
	branch: string;

	jobTitleCode: string;

	// L<PERSON><PERSON> thông tin quản lý trực tiếp của user theo
	managerAccountID: number;

	// for display
	role?: object; // role: *Role ;
	org?: object; //org?: *Organization ;

	// for action source
	jobTitleName?: string;
	department?: Department;
	subDepartmentCodes?: string[];

	// IsMain = true của userRole (của app Internal) sẽ được đồng bộ với isMain của userJobTitle
	isMain: boolean;

	// For app
	entity?: object; //entity?: *Entity ;
}
