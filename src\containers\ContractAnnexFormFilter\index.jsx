import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	DateRangePicker,
	FormInput,
	Row,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { formatDatetime, isEmptyObject, sanitize } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { useNavigate } from "@solidjs/router";
import { createSignal } from "solid-js";
import { useSearchParams } from "solid-start/router";
import * as XLSX from "xlsx";
import { getBidList } from "~/services/tender/bid.client";
import { getContractList } from "~/services/tender/contract.client";
import { formatDateYYYYMMDDHHIISS } from "~/utils/format";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";

export function ContractAnnexFormFilter() {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const toast = useToast();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);

	const [searchParams, setSearchParams] = useSearchParams();

	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			setSearchParams({
				q,
				page: undefined,
				limit: undefined,
			});
		},
	});

	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	const returnMapDataExportRequest = async (q) => {
		const resContract = await getContractList({
			q,
			offset: 0,
			limit: 1000,
			option: {
				total: true,
			},
		});

		let bidIDs = [];
		(resContract.data || [])?.forEach((contract) => {
			bidIDs = bidIDs.concat(contract.bidID || []);
		});
		bidIDs = Array.from(new Set(bidIDs));
		const bidMap = {};

		if (bidIDs.length > 0) {
			const resBidList = await getBidList({
				q: {bidIDs},
				offset: 0,
				limit: 1000,
				option: {}
			});

			(resBidList.data || []).forEach(
				(item) => (bidMap[item.bidID] = item)
			);
		}

		const contractRequestList = resContract?.data || [];
		return contractRequestList.map((item, index) => ({
			no: index+1, 
			contractNumber: item.contractNumber,
			beneficiaryName: item.beneficiaryName,
			phoneNumber: item.phoneNumber,
			procuringEntity: bidMap[item.bidID].procuringEntity,
			bidName: bidMap[item.bidID].bidName,
			startTime: formatDatetime(item.startTime, "MM/dd/yyyy"),
			endTime: formatDatetime(item.endTime, "MM/dd/yyyy"),
			signingDate: formatDatetime(item.signingDate,"MM/dd/yyyy"),
			expireDate: formatDatetime(item.expireDate,"MM/dd/yyyy"),
			contractValue: item.contractValue,
			numOfProduct: item.numOfProduct,
			// termOfPayment: item.termOfPayment,
			status: item.status
		}));
	};

	const handleExportFile = async () => {
		setIsLoadingExport(true);
		const q = JSON.parse(searchParams.q || "{}");
		
		const data = await returnMapDataExportRequest(q);
		if (Array.isArray(data) && data.length) {
			const fileName = `Contract_List_${formatDateYYYYMMDDHHIISS(
				new Date().toISOString()
			)}.xlsx`;

			const columnName = {
				no: t`contract:no`, 
				contractNumber: t`contract:contract_number`,
				beneficiaryName: t`contract:beneficiary_name`,
				phoneNumber: t`contract:phone_number`,
				procuringEntity: t`contract:procuring_entity`,
				bidName: t`contract:bid`,
				startTime: t`contract:start_time`,
				endTime: t`contract:end_time`,
				signingDate: t`contract:signing_date`,
				expireDate: t`contract:expire_date`,
				contractValue: t`contract:contract_value`,
				numOfProduct: t`contract:num_of_product`,
				// termOfPayment: t`contract:term_of_payment`,
				status: t`contract:contract_status`
			};

			const header = Object.keys(columnName).map((key) => columnName[key]);

			const dataWithHeader = [header, ...data.map((item) => Object.values(item))];

			const ws = XLSX.utils.aoa_to_sheet(dataWithHeader);

			const columnWidths = Object.keys(columnName).map((key) => {
				const columnHeader = columnName[key];
				const maxColumnDataLength = Math.max(
					...header.map((value) => (value ? value.toString().length : 0))
				);
				const maxColumnHeaderLength = columnHeader ? columnHeader.length : 0;
				return Math.max(maxColumnDataLength, maxColumnHeaderLength) * 1.2;
			});

			ws["!cols"] = columnWidths.map((width) => ({ width }));

			const wb = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, "List Contract");
			XLSX.writeFile(wb, fileName);
		} else {
			toast.error(t(`common:notify.action_fail`, {"error": "contracts not found"}));
		}
		setIsLoadingExport(false);
	}

	return (
		<Card>
			<CardBody>
				<form ref={form}>
					<Row class="row-gap-3">
						<Col xs={12} md={6} lg={4}>
							<FormInput
								name="contractNumber"
								type="search"
								label={t`contract:contract_number`}
								placeholder={t`contract:enter_contract_number`}
							/>
						</Col>
						<Col xs={12} md={6} lg={4}>
							<DateRangePicker
								name="createdTime"
								placeholder={[t`common:from_date`, t`common:to_date`]}
								label={t`contract:created_time`}
							/>
						</Col>

						<Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
							<Button
								color="secondary"
								startIcon={<FilterRemoveIcon />}
								onClick={onClearFilter}
							>
								{t`common:button.clearFilter`}
							</Button>

							{/* <Button
								color={"success"}
								loading={isLoadingExport()}
								onClick={handleExportFile}
								startIcon={<MdiMicrosoftExcel />}
							>
								{t("common:button.exportExcel")}
							</Button> */}

							<Button type="submit" color="success" startIcon={<MagnifyIcon />}>
								{t`common:button.applyButton`}
							</Button>
						</Col>
					</Row>
				</form>
			</CardBody>
		</Card>
	);
}
