import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { ProductMap, ProductSearchResponse } from "./product-map.model";
import { callAPI } from "../callAPI";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";

const URL = "/tender/core-tender/v1";

/** Return a list of product */
export async function getProductMapList(data: any): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/product-component/fuzzy`, data);
}

// ===================================================
// For QUERY
// ===================================================
export type ProductMapQuery = {};

export type QueryOption = MasterDataOption & {};