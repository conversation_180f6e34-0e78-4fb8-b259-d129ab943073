import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary } from "solid-js";
import { useNavigate } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ProductFormCreate } from "~/containers/ProductFormCreate";
import { createProduct } from "~/services/tender/product.client";

export default () => {
	return (
		<AppLayout
			pageTitle="Thêm sản phẩm"
			namespaces={["product"]}
			breadcrumbs={[BREADCRUMB.PRODUCT, BREADCRUMB.ADD_PRODUCT]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Thêm sản phẩm</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddProduct/>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddProduct() {
    const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();
	
    const hookForm = createForm({
		initialValues: {
            product: {
                price: 0,
                amount: 0,
                quantity: 0
            },
		},
		// validate: (values) => validateUserForm(values, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
            const payload = data;
            payload.productID = +payload.productID || 0
            payload.contractID = +payload.contractID || 0
			payload.lotID = +payload.lotID
			payload.groupMedicine = +payload.groupMedicine
			
            const res = await createProduct(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create product:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:create_success`);
			    navigate("/product", { replace: false });
            }

            return;
		},
	});

	return (
		<ProductFormCreate hookForm={hookForm}/>
	);
}