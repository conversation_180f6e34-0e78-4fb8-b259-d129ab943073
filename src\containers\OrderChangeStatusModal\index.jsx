import { Button, Tooltip, useToast, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createSignal } from "solid-js";
import { refetchRouteData } from "solid-start";
import ConfirmModal from "~/components/ConfirmModal";
import { updateOrderStatus } from "~/services/tender/order.client";
import { ORDER_STATE } from "~/services/tender/order.model";


export function OrderChangeStatusModal(props) {
	const { t } = useTranslate();
	const toast = useToast();

	const [isSubmitting, setIsSubmitting] = createSignal(false);

	async function onUpdateOrderStatus(status, onClose) {
        if(!props.orderID) {
            return;
        }
		setIsSubmitting(true);
        const orderID = +props.orderID;
		const res = await updateOrderStatus({
            orderID: orderID,
            status: status,
        });

		setIsSubmitting(false);

		if (res.status !== API_STATUS.OK) {
			console.error("[Error] Update order:", res);
			toast.error(t("common:notify.action_fail", { error: res.message }));
		} else {
			toast.success(t("common:notify.action_success"));
		}
		if (onClose) {
			onClose();
		}
		refetchRouteData();
	}

	return (
		<ConfirmModal
			title="Xác nhận đơn hàng"
			trigger={(openModal) => (
				<Tooltip content={props.disabled ? "" : t`common:tooltip.update`}>
                    <Button 
						color={props.color} 
						class="m-3" onClick={props.disabled ? false : openModal}>
                        {props.buttonText}
                    </Button>
				</Tooltip>
			)}
			footer={(onClose) => (
				<div class="d-flex gap-2">
					<Button
						color="success"
						onClick={() => onUpdateOrderStatus(ORDER_STATE.CONFIRMED, onClose)}
						loading={isSubmitting()}
					>
                        Xác nhận
					</Button>
				</div>
			)}
		>
			<p>Xin hãy xác nhận để cập nhật đơn hàng tiếp tục xử lý, thay đổi này sẽ được áp dụng sau khi bạn xác nhận.</p>
			
		</ConfirmModal>
	);
}
