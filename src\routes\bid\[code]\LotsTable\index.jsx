import { <PERSON><PERSON>, <PERSON>, Col, Row, Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow, Tooltip, useToast, useTranslate } from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { Index, Show, createSignal } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { LotChangeStatusModal } from "~/containers/LotChangeStatusModal";
import { BID_STATUS } from "~/services/tender/bid.model";
import { LOT_STATUS, LOT_STATUS_LABEL_COLOR } from "~/services/tender/lot.model";
import EditIcon from "~icons/mdi/square-edit-outline";
import AddIcon from "~icons/mdi/plus";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";
import { getBidLotList } from "~/services/tender/bid.client";
import { useSearchParams } from "solid-start";
import { formatDateYYYYMMDDHHIISS } from "~/utils/format";
import * as XLSX from "xlsx";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";

export function LotsTable(props) {
	const { t } = useTranslate();
	const toast = useToast();
	const [searchParams, setSearchParams] = useSearchParams();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);
	
	function calculateAmount(props) {
		if (!props.lots) {
			return 0;
		}
		var num = 0;
		props.lots.forEach((lot) => {
			num = num + lot.lotAmount;
		})
		return num;
	};

	const returnMapDataExportRequest = async (q) => {
		const resLot = await getBidLotList({
			q,
			offset: 0,
			limit: 1000,
			option: {
				total: true,
			},
		});

		const lotRequestList = resLot?.data;
		return lotRequestList.map((item) => ({
			lotID: item.lotID,
			lotLineID: item.lotLineID,
			productID: item.productID,
			lotName: item.lotName,
			registrationNo: item.registrationNo,
			unit: item.unit,
			groupMedicine: `Nhóm ${item.groupMedicine}`,
			quantity: item.quantity,
			lotPrice: item.lotPrice,
			lotAmount: item.lotAmount,
			status: item.status,
			note: item.note
		}));
	};

	const handleExportFile = async () => {
		if(!props.bid) {
			toast.error(t(`common:notify.action_fail`));
			return;
		}
		setIsLoadingExport(true);
		const q = {bidID: props.bid.bidID};
		
		const data = await returnMapDataExportRequest(q);
		if (Array.isArray(data) && data.length) {
			const fileName = `Lot_List_${formatDateYYYYMMDDHHIISS(
				new Date().toISOString()
			)}_${props.bid.itb}.xlsx`;

			const columnName = {
				lotId: "Lot ID", 
				lotLineID: "Lot No",
				productId: "Product ID",
				lotName: t`bid:lot.name`,
				registrationNo: t`bid:lot.registration_number`,
				unit: t`bid:lot.unit`,
				groupMedicine: t`bid:lot.group_medicine`,
				quantity: t`bid:lot.number_bidder`,
				lotPrice: t`bid:lot.lot_final_price`,
				lotAmount: t`bid:lot.lot_total_price`,
				status: t`bid:lot.status`,
				note: `Note`
			};

			const header = Object.keys(columnName).map((key) => columnName[key]);

			const dataWithHeader = [header, ...data.map((item) => Object.values(item))];

			const ws = XLSX.utils.aoa_to_sheet(dataWithHeader);

			const columnWidths = Object.keys(columnName).map((key) => {
				const columnHeader = columnName[key];
				const maxColumnDataLength = Math.max(
					...header.map((value) => (value ? value.toString().length : 0))
				);
				const maxColumnHeaderLength = columnHeader ? columnHeader.length : 0;
				return Math.max(maxColumnDataLength, maxColumnHeaderLength) * 1.2;
			});

			ws["!cols"] = columnWidths.map((width) => ({ width }));

			const wb = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, "List Lot");
			XLSX.writeFile(wb, fileName);
		} else {
			toast.error(t(`common:notify.action_fail`));
		}
		setIsLoadingExport(false);
	}

    return (
		<>
		<Row class="gap-3">
			<Show when={props.tab === "lot"}>
				<Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
					<AuthContent privilege={PRIVILEGE.BID.EXPORT_LOT}>
						<Show when={props.lots && props.lots.length > 0}>
							<Button
								color={"success"}
								loading={isLoadingExport()}
								onClick={handleExportFile}
								startIcon={<MdiMicrosoftExcel class="fs-4" />}
							>
								{t("common:button.exportExcel")}
							</Button>
						</Show>
					</AuthContent>
					<AuthContent privilege={PRIVILEGE.BID.CREATE_LOT}>
						<Show when={true}>
							<Button
								color={"second"}
								disabled
								startIcon={<MdiMicrosoftExcel class="fs-4" />}
							>
								{t("common:button.importExcel")}
							</Button>
						</Show>
					</AuthContent>
					<AuthContent privilege={PRIVILEGE.BID.CREATE_LOT}>
						<Button
							color={props.bid.status === BID_STATUS.WAITING ? "success" : "second"}
							href={`../lot/new`}
							disabled={props.bid.status !== BID_STATUS.WAITING}
							startIcon={<AddIcon class="fs-4" />}
						>
							{t`bid:add_lot`}
						</Button>
					</AuthContent>
				</Col>
				<br/>
			</Show>
		</Row>
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`bid:no`}</TableHeaderCell>
						<TableHeaderCell>Lô</TableHeaderCell>
						<TableHeaderCell>Product ID</TableHeaderCell>
						<TableHeaderCell class="col-2">{t`bid:lot.name`}</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								Số lượng
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "center" }}>
								{t`bid:lot.unit`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "center" }}>
								{t`bid:lot.group_medicine`}
							</div>
						</TableHeaderCell>
						
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t`bid:lot.lot_final_price`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t`bid:lot.lot_total_price`}
							</div>
						</TableHeaderCell>
                        <TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t`bid:lot.bid_guarantee_amount`}
							</div>
						</TableHeaderCell>
                        <Show when={true}>
							<TableHeaderCell>
								<div style={{ "text-align": "center" }}>
									{t`bid:lot.status`}
								</div>
							</TableHeaderCell>
						</Show>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.lots}
						fallback={
							<TableRow>
								<TableCell colSpan={10} style={{ "text-align": "center" }}>
									{t`bid:lot.not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(lot, index) => <LotTableRow bid={props.bid} item={lot()} index={index+1}/>}
					</Index>
					<TableRow>
						<TableCell colSpan={6}></TableCell>
						<TableCell>Tổng tiền:</TableCell>
						<TableCell></TableCell>
						<TableCell>
							<div style={{ "text-align": "right" }}>
								{formatNumber(calculateAmount(props))}
							</div>
						</TableCell>
					</TableRow>
					<TableRow>
						<TableCell colSpan={6}></TableCell>
						<TableCell>Tổng tiền dự thầu:</TableCell>
						<TableCell></TableCell>
						<TableCell>
							<div style={{ "text-align": "right" }}>
								{formatNumber(props.bid.bidPrice)}
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
		</>
	);
}

function LotTableRow(props) {
    const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.index}</TableCell>
			<TableCell>{props.item.lotLineID}</TableCell>
			<TableCell>
				{props.item.productID > 0 ? props.item.productID : "Chờ cập nhật"}
			</TableCell>
			<TableCell>
				{props.item.lotName}
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.quantity)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "center" }}>
					{props.item.unit}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "center" }}>
					{props.item.groupMedicine}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.lotPrice)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.lotAmount)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.guaranteeAmount)}
				</div>
			</TableCell>
			<Show when={true}>
				<TableCell>
					<div class="d-flex justify-content-center align-items-center gap-1">
						<LotChangeStatusModal 
							lotID={props.item.lotID}
							// disabled={props.item.status !== LOT_STATUS.WAITING}
							color={LOT_STATUS_LABEL_COLOR[props.item.status]} 
							buttonText={props.item.status}
						/>
					</div>
				</TableCell>
			</Show>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`bid:lot.edit_lot`}>
						<Button
							class="p-2"
							variant="outline"
							color="secondary"
							startIcon={<EditIcon />}
							href={`/bid/${props.bid.bidID}/lot/edit?lotID=${props.item.lotID}`}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}