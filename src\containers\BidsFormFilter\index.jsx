import { createForm } from "@felte/solid";
import { useNavigate } from "@solidjs/router";
import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	DateRangePicker,
	FormInput,
	FormSelect,
	Row,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS, formatDatetime, isEmptyObject, sanitize } from "@buymed/solidjs-component/utils";
import { useSearchParams } from "solid-start/router";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";
import { createSignal } from "solid-js";
import * as XLSX from "xlsx";
import { getBidList } from "~/services/tender/bid.client";
import { formatDateYYYYMMDDHHIISS } from "~/utils/format";
import { BID_CATEGORY_LABEL, BID_SUBMISSION_METHOD_LABEL, BID_SUBMISSION_METHOD_OPTIONS } from "~/services/tender/bid.model";
import { REGION_LABEL, REGION_OPTIONS, YEAR_OPTIONS } from "~/services/tender/region.model";


export function BidsFormFilter(props) {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const toast = useToast();
	const [isLoadingExport, setIsLoadingExport] = createSignal(false);

	const [searchParams, setSearchParams] = useSearchParams();

	const { form, setFields, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			if (q.year) {
				q.year = +q.year
			}
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			setSearchParams({
				q,
				page: undefined,
				limit: undefined,
			});
		},
	});

	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		
		Object.keys(data()).forEach(e => {
			switch(e) {
				case 'year': {
					setFields(e, "");
					break;
				}
				case 'performanceSubmissionMethod': {
					setFields(e, "");
					break;
				}
				case 'region': {
					setFields(e, "");
					break;
				}
				default:{
					unsetField(e);
				}
			}
		});
		navigate(window.location.pathname);
	}

	const returnMapDataExportRequest = async (q) => {
		const resBid = await getBidList({
			q,
			offset: 0,
			limit: 1000,
			option: {
				total: true,
			},
		});

		const bidRequestList = resBid?.data || [];
		return bidRequestList.map((item, index) => ({
			no: index+1, 
			year: item.year,
			itb: item.itb,
			procuringEntity: item.procuringEntity,
			mainCategory: item.mainCategory ? BID_CATEGORY_LABEL[item.mainCategory] : item.mainCategory,
			openingDate: formatDatetime(item.openingDate, "MM/dd/yyyy"),
			closingDate: formatDatetime(item.closingDate, "MM/dd/yyyy"),
			numOfProduct: item.numOfProduct,
			bidPrice: item.bidPrice,
			contractExecutionPeriod: item.contractExecutionPeriod,
			contractExecutionPeriodKind: item.contractExecutionPeriodKind,
			contractTerminationDate: formatDatetime(item.contractTerminationDate, "MM/dd/yyyy"),
			securitySubmissionMethod: item.securitySubmissionMethod ? t(BID_SUBMISSION_METHOD_LABEL[item.securitySubmissionMethod]) : "",
			securityValidity: item.securityValidity,
			securitySubmissionAmount: item.securitySubmissionAmount,
			securitySubmissionDate: formatDatetime(item.securitySubmissionDate, "MM/dd/yyyy"),
			performanceSubmissionMethod: item.performanceSubmissionMethod ? t(BID_SUBMISSION_METHOD_LABEL[item.performanceSubmissionMethod]) : "",
			// performanceValidity: item.performanceValidity,
			performanceSubmissionAmount: item.performanceSubmissionAmount,
			performanceSubmissionDate: formatDatetime(item.performanceSubmissionDate, "MM/dd/yyyy"),
			winningQuantity: item.winningQuantity,
			winningPrice: item.winningPrice,
			winningDecision: item.winningDecision,
			contractNo: item.contractNo,
			region: item.region ? REGION_LABEL[item.region] : item.region,
			status: item.status,
		}));
	};

	const handleExportFile = async () => {
		setIsLoadingExport(true);
		const q = JSON.parse(searchParams.q || "{}");
		
		const data = await returnMapDataExportRequest(q);
		if (Array.isArray(data) && data.length) {
			const fileName = `Bid_List_${formatDateYYYYMMDDHHIISS(
				new Date().toISOString()
			)}.xlsx`;

			const columnName = {
				no: t`bid:no`, 
				year: t`bid: year`,
				itb: t`bid:invitation_of_bid`,
				procuringEntity: t`bid:procuring_entity`,
				mainCategory: t`bid:main_category`,
				openingDate: t`bid:bid_opening_date`,
				closingDate: t`bid:bid_closing_date`,
				numOfProduct: t`bid:bid_quantity`,
				bidPrice: t`bid:bid_price`,
				contractExecutionPeriod: t`bid:contract_execution_period`,
				contractExecutionPeriodKind: t`bid:contract_execution_periodKind`,
				contractTerminationDate: t`bid:contract_termination_date`,
				securitySubmissionMethod: t`bid:security_submission_method`,
				securityValidity: t`bid:security_validity`,
				securitySubmissionAmount: t`bid:security_submission_amount`,
				securitySubmissionDate: t`bid:security_submission_date`,
				performanceSubmissionMethod: t`bid:performance_submission_method`,
				// performanceValidity: t`bid:performance_validity`,
				performanceSubmissionAmount: t`bid:performance_submission_amount`,
				performanceSubmissionDate: t`bid:performance_submission_date`,

				winningQuantity: t`bid:winning_bid_quantity`,
				winningPrice: t`bid:winning_bid_price`,
				winningDecision: t`bid:winning_bid_decision`,
				contractNo: t`bid:contract_number`,
				region: t`bid:region`,
				status: t`bid:bid_status`
			};

			const header = Object.keys(columnName).map((key) => columnName[key]);

			const dataWithHeader = [header, ...data.map((item) => Object.values(item))];

			const ws = XLSX.utils.aoa_to_sheet(dataWithHeader);

			const columnWidths = Object.keys(columnName).map((key) => {
				const columnHeader = columnName[key];
				const maxColumnDataLength = Math.max(
					...header.map((value) => (value ? value.toString().length : 0))
				);
				const maxColumnHeaderLength = columnHeader ? columnHeader.length : 0;
				return Math.max(maxColumnDataLength, maxColumnHeaderLength) * 1.2;
			});

			ws["!cols"] = columnWidths.map((width) => ({ width }));

			const wb = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, "List Bid");
			XLSX.writeFile(wb, fileName);
		} else {
			toast.error(t(`common:notify.action_fail`, {"error": "bids not found"}));
		}
		setIsLoadingExport(false);
	}

	return (
		<Card>
			<CardBody>
				<form ref={form}>
					<Row class="row-gap-3">
						<Col xs={12} md={6} lg={4}>
							<FormInput
								name="invitationOfBid"
								type="search"
								label={t`bid:invitation_of_bid`}
								placeholder={t`bid:enter_invitation_of_bid`}
							/>
						</Col>
						<Col xs={12} md={6} lg={4}>
							<FormInput
								name="procuringEntity"
								type="search"
								label={t`bid:procuring_entity`}
								placeholder={t`bid:enter_procuring_entity`}
							/>
						</Col>
						<Col xs={12} md={6} lg={4}>
							<DateRangePicker
								name="createdTime"
								placeholder={[t`common:from_date`, t`common:to_date`]}
								label={t`bid:created_time`}
							/>
						</Col>

						<Col xs={12} md={6} lg={4}>
							<FormSelect
								name="year"
								id="year"
								options={YEAR_OPTIONS()}
								placeholder="Chọn"
								label="Năm dự thầu"
							/>
						</Col>

						<Col xs={12} md={6} lg={4}>
							<FormSelect
								name="performanceSubmissionMethod"
								id="performanceSubmissionMethod"
								options={BID_SUBMISSION_METHOD_OPTIONS(t)}
								placeholder="Chọn"
								label="Hình thức đảm bảo hợp đồng"
							/>
						</Col>

						<Col xs={12} md={6} lg={4}>
							<FormSelect
								name="region"
								id="region"
								options={REGION_OPTIONS(t)}
								placeholder="Chọn"
								label="Vùng"
							/>
						</Col>

						<Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
							<Button
								color="secondary"
								startIcon={<FilterRemoveIcon />}
								onClick={onClearFilter}
							>
								{t`common:button.clearFilter`}
							</Button>

							<Button
								color={"success"}
								loading={isLoadingExport()}
								onClick={handleExportFile}
								startIcon={<MdiMicrosoftExcel />}
							>
								{t("common:button.exportExcel")}
							</Button>

							<Button type="submit" color="success" startIcon={<MagnifyIcon />}>
								{t`common:button.applyButton`}
							</Button>
						</Col>
					</Row>
				</form>
			</CardBody>
		</Card>
	);
}
