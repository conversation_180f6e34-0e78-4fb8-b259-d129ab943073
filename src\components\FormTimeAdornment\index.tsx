import { JSXElement, Show, createMemo, mergeProps, splitProps } from "solid-js";
import { FormInput, FormInputProps } from "@buymed/solidjs-component/components";
import { FormSelect, Option } from "@buymed/solidjs-component/components";
import styles from "./styles.module.scss";

export const TimeAdornmentList = [
	{
		name: "Tháng",
		code: "MONTH",
	},
	{
		name: "<PERSON><PERSON><PERSON>",
		code: "DAY",
	},
];

export function toKindOptions(kinds: typeof TimeAdornmentList) {
	return kinds.map((kind) => ({
		label: `${kind.name}`,
		value: kind.code,
	}));
}

export interface FormTimeAdornmentInputProps extends FormInputProps {
	/**
	 * array of kinds objects, allow users to customize their own kinds array
	 */
	kinds?: string[];

	/**
	 * A string of all class name you want applied to the component.
	 */
	kindCodeName: string;

	/**
	 * allow users to override the default kinds options
	 */
	kindOptions?: Option[];

	/**
	 * if true, than we display a select for selecting kind
	 */
	showKind?: boolean;
}

const defaultProps = {
	kindCodeName: "kindCode",
	showKind: true,
};

export function FormTimeAdornment(props: FormTimeAdornmentInputProps): JSXElement {
	const merged = mergeProps(defaultProps, props);
	const [local, other] = splitProps(merged, [
		"kinds",
		"kindCodeName",
		"kindOptions",
		"disabled",
		"showKind",
	]);

	const options = createMemo(() => {
		if (local.kindOptions) {
			return local.kindOptions;
		}

		if (local.kinds) {
			return toKindOptions(
				TimeAdornmentList.filter((c) => local.kinds?.includes(c.code))
			);
		}

		return toKindOptions(TimeAdornmentList);
	});

	return (
		<Show
			when={local.showKind}
			fallback={
				<FormInput
					inputMode="text"
					type="text"
					disabled={local.disabled}
					{...other}
				/>
			}
		>
			<FormInput
				inputMode="text"
				type="number"
				endAdornment={() => (
					<FormSelect
						name={local.kindCodeName}
						options={options()}
						class={styles["kind-select-inline"]}
						disabled={local.disabled}
					/>
				)}
				showDivider
				disabled={local.disabled}
				{...other}
			/>
		</Show>
	);
}
