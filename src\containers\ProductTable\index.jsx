import { <PERSON><PERSON>, <PERSON>, Row, Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { Index, Show } from "solid-js";
import { A } from "solid-start";
import BMTablePagination from "~/components/table/BMTablePagination";
import { DOSAGE_LABEL, LOT_STATUS_LABEL_COLOR } from "~/services/tender/lot.model";
import EditIcon from "~icons/mdi/square-edit-outline";

export function ProductTable(props) {
    const { t } = useTranslate();
    return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`contract:product.id`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:product.code`}</TableHeaderCell>
						<TableHeaderCell class="col-2">{t`contract:product.name`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:name`}/Phụ lục</TableHeaderCell>
						<TableHeaderCell>{t`contract:product.registration_number`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:product.group_medicine`}</TableHeaderCell>
						<TableHeaderCell>VAT</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t`contract:product.number_bidder`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t`contract:product.lot_final_price`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "center" }}>
								Đơn vị
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
								{t`contract:product.lot_total_price`}
							</div>
						</TableHeaderCell>
                        <TableHeaderCell>
							<div style={{ "text-align": "right" }}>
							{t`contract:product.quantity_processing`}
							</div>
						</TableHeaderCell>
                        <TableHeaderCell>
							<div style={{ "text-align": "right" }}>
							{t`contract:product.quantity_remaining`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell>
							<div style={{ "text-align": "right" }}>
							{t`contract:product.quantity_sold`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.products}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`contract:product.not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(product) => <ProductTableRow
							page={props.page}
							item={product()} 
							link={props.link}
							productView={props.productMap[product().productID]}
							contractView={props.contractMap[product().contractID]}/>}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

function ProductTableRow(props) {
    const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.item.productID}</TableCell>
			<TableCell>{props.item.code}</TableCell>
			<TableCell>
				{/* <Show when={props.productView}>
					{props.productView.lotName || props.productView.name}
				</Show> */}
				{props.item.productName}
			</TableCell>
			<Show when={true}>
				<Show when={props.contractView}>
					<TableCell>
						<Show when={props.contractView.bidID}>
							<Show when={props.contractView.annexNumber}>
								<A href={`/contract/${props.contractView.contractCode}/annex/info?code=${props.contractView.code}`}>
									{props.contractView.name}
								</A>
							</Show>
							<Show when={props.contractView.contractNumber}>
								<A href={`/contract/${props.contractView.code}`}>
									{props.contractView.name}
								</A>
							</Show>
						</Show>
						<Show when={!props.contractView.bidID}>
							<Show when={props.contractView.annexNumber}>
								<A href={`/contract-individual/${props.contractView.contractCode}/annex/info?code=${props.contractView.code}`}>
									{props.contractView.name}
								</A>
							</Show>
							<Show when={props.contractView.contractNumber}>
								<A href={`/contract-individual/${props.contractView.code}`}>
									{props.contractView.name}
								</A>
							</Show>
						</Show>
						
					</TableCell>
				</Show>
				<Show when={!props.contractView}>
					<TableCell>
						{props.item.contractCode}
					</TableCell>
				</Show>
			</Show>
			<TableCell>{props.item.registrationNo}</TableCell>
			<TableCell>
				<div style={{ "text-align": "center" }}>
					{props.item.groupMedicine}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "center" }}>
					{props.item.vat}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					<Show when={props.page == "contract"} fallback={
						formatNumber(props.item.quantity)
					}>
						{props.item.quantityInitial ? formatNumber(props.item.quantityInitial) : formatNumber(props.item.quantity)}
					</Show>
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.price)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "center" }}>
					{props.item.unit}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.amount) || "_"}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.quantityProcessing || 0)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
				{formatNumber(props.item.quantityRemain || 0)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
				{formatNumber(props.item.quantitySold || 0)}
				</div>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`contract:product.view_product`}>
						<Button
							class="p-2"
							variant="outline"
							color="primary"
							href={`/product/info?code=${props.item.code}`}
							startIcon={<EditIcon />}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}