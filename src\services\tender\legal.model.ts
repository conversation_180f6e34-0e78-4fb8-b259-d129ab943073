
export interface LegalEntityBank {
    accountNumber: string,
    bankName: string,
    bankBranch: string,
    bankCode: string
}

export interface LegalEntity {
    code: string;
    countryCode: string;
    name: string,
    taxCode: string,
    address: string,
    email: string,
    tel: string,
    banks: LegalEntityBank[]
}


export const LEGAL_ENTITY_OPTIONS = [
	{
		value: "MEDX_HCM",
		label: "CÔNG TY TNHH DƯỢC PHẨM MEDX",
		data: {
			code: "MEDX_HCM",
			countryCode: "VN",
			name: "CÔNG TY TNHH DƯỢC PHẨM MEDX",
			taxCode: "**********",
			address:
				"Tầng 3, số 164 <PERSON><PERSON>, Phường 12, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>h<PERSON>, Việt Nam",
			email: "<EMAIL>",
			tel: "**********",
			banks: [
				{
					bankName: "MSB BANK",
					bankBranch: "PGD TÂN THUẬN",
					bankCode: "MCOBVNVX",
					accountNumber: "**************",
				},
				{
					bankName: "AB BANK",
					bankBranch: "Chi Nhánh HCM",
					bankCode: "ABBKVNVX",
					accountNumber: "*************",
				},
			],
			legalEntityBranch: "TENDER",
		},
	},
	{
		value: "BML_HCM",
		label: "CÔNG TY TNHH BUYMED LOGISTICS",
		data: {
			code: "BML_HCM",
			countryCode: "VN",
			name: "CÔNG TY TNHH BUYMED LOGISTICS",
			taxCode: "**********",
			address:
				"225 đường Mã Lò, Phường Bình Trị Đông A, Quận Bình Tân, Thành phố Hồ Chí Minh, Việt Nam",
			email: "<EMAIL>",
			tel: "",
			banks: [],
			legalEntityBranch: "TENDER",
		},
	},
	{
		value: "TAYAU",
		label: "CÔNG TY CỔ PHẦN ĐẦU TƯ VÀ PHÁT TRIỂN TÂY ÂU",
		data: {
			code: "TAYAU",
			countryCode: "VN",
			name: "CÔNG TY CỔ PHẦN ĐẦU TƯ VÀ PHÁT TRIỂN TÂY ÂU",
			taxCode: "**********",
			address: "2/67 Thiên Phước, Phường 9, Quận Tân Bình, Thành phố Hồ Chí Minh, Việt Nam",
			email: "<EMAIL>",
			tel: "***********-62",
			banks: [
				{
					bankName: "Ngân Hàng Vietcombank",
					bankBranch: "TPHCM",
					bankCode: "VCB",
					accountNumber: "*************",
				},
				{
					bankName: "Ngân Hàng ACB",
					bankBranch: "TPHCM",
					bankCode: "ACB",
					accountNumber: "********",
				},
				{
					bankName: "Ngân Hàng TMCP Quân Đội",
					bankBranch: "CN Bắc Sài Gòn - TPHCM",
					bankCode: "MB",
					accountNumber: "*************",
				},
				{
					bankName: "Ngân Hàng ABBANK",
					bankBranch: "CN TPHCM",
					bankCode: "ABB",
					accountNumber: "*************",
				},
				{
					bankName: "Ngân Hàng Techcombank",
					bankBranch: "CN Thắng Lợi - TPHCM",
					bankCode: "TCB",
					accountNumber: "**************",
				},
				{
					bankName: "Ngân hàng TMCP An Bình",
					bankBranch: "Chi nhánh TP. Hồ Chí Minh",
					bankCode: "ABB",
					accountNumber: "*************",
				},
			],
			legalEntityBranch: "TAYAU",
		},
	},
];

export const LEGAL_ENTITY_BANK_OPTIONS = [
    {
        value: "MCOBVNVX", 
        label: "Tài khoản ************** tại MSB BANK - PGD TÂN THUẬN", 
        data: {
            bankName: "MSB BANK",
            bankBranch: "PGD TÂN THUẬN",
            bankCode: "MCOBVNVX",
            accountNumber: "**************"
        }
    },
    {
        value: "ABBKVNVX", 
        label: "Tài khoản ************* tại AB BANK - Chi Nhánh HCM", 
        data: {
            bankName: "AB BANK",
            bankBranch: "Chi Nhánh HCM",
            bankCode: "ABBKVNVX",
            accountNumber: "*************"
        }
    },
	{
        value: "VCB*************",
        label: "Tài khoản ************* tại Ngân Hàng Vietcombank - TPHCM",
        data: {
            bankName: "Ngân Hàng Vietcombank",
            bankBranch: "TPHCM",
            bankCode: "VCB",
            accountNumber: "*************"
        }
    },
    {
        value: "ACB********",
        label: "Tài khoản ******** tại Ngân Hàng ACB - TPHCM",
        data: {
            bankName: "Ngân Hàng ACB",
            bankBranch: "TPHCM",
            bankCode: "ACB",
            accountNumber: "********"
        }
    },
    {
        value: "***************",
        label: "Tài khoản ************* tại Ngân Hàng TMCP Quân Đội – CN Bắc Sài Gòn - TPHCM",
        data: {
            bankName: "Ngân Hàng TMCP Quân Đội",
            bankBranch: "CN Bắc Sài Gòn - TPHCM",
            bankCode: "MB",
            accountNumber: "*************"
        }
    },
    {
        value: "ABB*************",
        label: "Tài khoản ************* tại Ngân Hàng ABBANK – CN TPHCM",
        data: {
            bankName: "Ngân Hàng ABBANK",
            bankBranch: "CN TPHCM",
            bankCode: "ABB",
            accountNumber: "*************"
        }
    },
    {
        value: "TCB**************",
        label: "Tài khoản ************** tại Ngân Hàng Techcombank - CN Thắng Lợi - TPHCM",
        data: {
            bankName: "Ngân Hàng Techcombank",
            bankBranch: "CN Thắng Lợi - TPHCM",
            bankCode: "TCB",
            accountNumber: "**************"
        }
    },
    {
        value: "ABB*************",
        label: "Tài khoản ************* tại Ngân hàng TMCP An Bình - Chi nhánh TP. Hồ Chí Minh",
        data: {
            bankName: "Ngân hàng TMCP An Bình",
            bankBranch: "Chi nhánh TP. Hồ Chí Minh",
            bankCode: "ABB",
            accountNumber: "*************"
        }
    }
];