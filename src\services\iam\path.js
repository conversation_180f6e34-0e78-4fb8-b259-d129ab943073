import { isServer } from "solid-js/web";
import { useRequest } from "solid-start/server";

export function getSSOLoginUri() {
	const loginURL = new URL(`${import.meta.env.VITE_SSO_HOST}/oauth/authorize`);
	loginURL.searchParams.set("clientID", import.meta.env.VITE_APP_CLIENT_ID);
	loginURL.searchParams.set("responseType", "code");
	loginURL.searchParams.set("redirectUri", getLoginCallbackUri());

	return loginURL.toString();
}

export function getSSOSwitchAccountUri() {
	const loginURL = new URL(`${import.meta.env.VITE_SSO_HOST}/chooseaccount`);
	loginURL.searchParams.set("clientID", import.meta.env.VITE_APP_CLIENT_ID);
	loginURL.searchParams.set("responseType", "code");
	loginURL.searchParams.set("redirectUri", getLoginCallbackUri());

	return loginURL.toString();
}

export function getSSOLoginUriWithParams(newAccount = false, loginHint = "", authUser = 0) {
	const loginURL = new URL(`${import.meta.env.VITE_SSO_HOST}/oauth/authorize`);
	loginURL.searchParams.set("clientID", import.meta.env.VITE_APP_CLIENT_ID);
	loginURL.searchParams.set("responseType", "code");
	if (newAccount) {
		loginURL.searchParams.set("new", "true");
	}
	if (loginHint) {
		loginURL.searchParams.set("loginHint", loginHint);
	}
	if (authUser) {
		loginURL.searchParams.set("authUser", authUser + "");
	} else {
		loginURL.searchParams.set("manualSelection", "true");
	}
	loginURL.searchParams.set("redirectUri", getLoginCallbackUri());

	return loginURL.toString();
}

function getLoginCallbackUri() {
	let host = "",
		redirectURI = "";
	if (isServer) {
		const context = useRequest();
		const url = new URL(context.request.url);
		host = url.origin;
		redirectURI = `${url.pathname}${url.search}`;
	} else {
		host = document.location.origin;
		redirectURI = `${document.location.pathname}${document.location.search}`;
	}

	const loginCallbackURL = new URL(`${host}/sso/login-callback`);
	loginCallbackURL.searchParams.set("redirect", redirectURI);

	return loginCallbackURL.toString();
}

export function getSSOLogoutUri() {
	const logoutURL = new URL(`${import.meta.env.VITE_SSO_HOST}/logout`);
	logoutURL.searchParams.set("redirectUri", getLogoutRedirectUri());

	return logoutURL.toString();
}

function getLogoutRedirectUri() {
	let host = "";
	if (isServer) {
		const context = useRequest();
		const url = new URL(context.request.url);
		host = url.origin;
	} else {
		host = document.location.origin;
	}

	const logoutCallbackURL = new URL(`${host}/sso/logout-callback`);

	return logoutCallbackURL.toString();
}
