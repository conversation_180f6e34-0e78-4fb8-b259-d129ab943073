import { FormAutocomplete, FormSelect, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS, debounce, sortBy } from "@buymed/solidjs-component/utils";
import { createEffect, createResource, createSignal, splitProps } from "solid-js";
import { getListProductUnitConvert } from "~/services/tender/product.client";

export function parseOption(unit) {
	return {
		value: unit.unitName,
		label: unit.unitName,
		data: unit,
	};
}

export function UnitSelectAutocomplete(props) {
	const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);

	const { t } = useTranslate();
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);

	// createEffect(() => {
	// 	console.log(props.productID)
	// })
	
	const [mapOptions] = createResource(
		() => [search, props.productID],
		async ([search, productID]) => {
			const query = {}
			
			if (productID) {
				query.productIDs = [+productID];
			} 

			const res = await getListProductUnitConvert({q: query});

			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch getListProductUnitConvert", res);
				return [];
			}

			let options = res.data.map((item) => parseOption(item));

			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					options.push(lastOption());
				}
			}

			options = sortBy(options, "label");
			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	function onInputChange(e) {
		setSearch(e.target.value);
	}
	const debouncedOnInputChange = debounce(onInputChange, 500);

	return (
		<FormSelect
			name={local.name}
			options={mapOptions()}
			placeholder="Chọn đơn vị"
			// onInputChange={debouncedOnInputChange}
			// isLoading={mapOptions.loading}
			// renderOption={(props, { data }) => (
			// 	<li {...props}>
			// 		<b>{data.unitName}</b>
			// 	</li>
			// )}
			onChange={(e) => {
				setLastOption(e);
			}}
			{...other}
		/>
	);
}
