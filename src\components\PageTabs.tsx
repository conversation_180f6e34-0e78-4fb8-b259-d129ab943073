import { Tab, Tabs, ValidTabLabel } from "@buymed/solidjs-component/components";
import { Index, JSXElement } from "solid-js";
import { useSearchParams } from "solid-start/router";

interface PageTabsProps {
	/** List of tab labels */
	tabs: ValidTabLabel[];
	defaultTab?: number;
}

export function PageTabs(props: PageTabsProps): JSXElement {
	const [searchParams, setSearchParams] = useSearchParams();
	let defaultTab = 0;

	if(props.defaultTab) {
		defaultTab = props.defaultTab
	}

	function onTabChange(_, newTab) {
		setSearchParams({ tab: newTab ?? undefined });
	}

	return (
		<Tabs value={searchParams.tab === undefined ? defaultTab : +searchParams.tab} onTabChange={onTabChange}>
			<Index each={props.tabs}>{(tab, i) => <Tab label={tab()} index={i} />}</Index>
		</Tabs>
	);
}
