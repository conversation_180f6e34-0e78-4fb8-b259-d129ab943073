import {
	<PERSON><PERSON>,
	Card,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
	Tooltip,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { Index, Show } from "solid-js";
import { A, useParams } from "solid-start";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import BMTablePagination from "~/components/table/BMTablePagination";
import { BID_STATUS_LABEL_COLOR } from "~/services/tender/bid.model";
import { CONTRACT_STATUS_LABEL_COLOR } from "~/services/tender/contract.model";
import EditIcon from "~icons/mdi/square-edit-outline";
import MdiStickerTextOutline from '~icons/mdi/sticker-text-outline';


export function ContractAnnexTable(props) {
	const { t } = useTranslate();

	return (
		<>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`contract:annex.no`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:annex.annex_number`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:annex.name`}</TableHeaderCell>
                        <TableHeaderCell>{t`contract:annex.open_date`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:annex.expire_date`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:annex.content`}</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.annexes}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`contract:annex.not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(annex) => <ContractAnnexTableRow item={annex()} bid={props.bidMap}/>}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</>
	);
}

function ContractAnnexTableRow(props) {
	const { t } = useTranslate();
	const param = useParams();

	return (
		<TableRow>
			<TableCell>{props.item.annexID}</TableCell>
			<TableCell>
                <A
                    href={`/contract/${props.item.contractCode}/annex/info?code=${props.item.code}`}
                >
                    {props.item.annexNumber}
                </A>
            </TableCell>
			<TableCell>{props.item.name}</TableCell>
			<TableCell>{formatDatetime(props.item.contractDate, "dd/MM/yyyy")}</TableCell>
			<TableCell>{formatDatetime(props.item.expireDate, "dd/MM/yyyy")}</TableCell>
			<TableCell>{props.item.content}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<AuthContent privilege={PRIVILEGE.CONTRACT.VIEW}>
						<Tooltip content={t`contract:edit_contract`}>
							<Button
								class="p-2"
								variant="outline"
								color="secondary"
								startIcon={<EditIcon />}
								href={`/contract/${param.code}/annex/info?code=${props.item.code}`}
							/>
						</Tooltip>
					</AuthContent>
				</div>
			</TableCell>
		</TableRow>
	);
}
