import {
    <PERSON><PERSON>,
    Card,
    CardBody,
    Col,
    Row,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeaderCell,
    TableRow,
    useTranslate
} from "@buymed/solidjs-component/components";
import { formatCurrency, formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { createSignal, Index, Show } from "solid-js";
import { A } from "solid-start";
import { ORDER_STATE_LABEL, OrderStatusLabelColor } from "~/services/tender/order.model";
import { InvoiceInfo } from "../InvoiceInfo";
import MdiArrowDown from '~icons/mdi/arrow-down';
import MdiArrowRight from '~icons/mdi/arrow-right';

export function LabelInformation(props) {
    return (
        <Row class="row-gap-3">
            <Col xs={12} md={6} lg={4} style={{"font-weight": "bold"}}>
                <p>{props.label}</p>
            </Col>
            <Col xs={12} md={6} lg={6}>
                <Show when={props.value}>
                    <p>{props.value}</p>
                </Show>
                {props.children}
            </Col>
        </Row>
    );
}

export function OrderInformation(props) {
	const { t } = useTranslate();
   
	return (
        <>
            <Row class="row-gap-3">
                <Col xs={12} md={6} lg={6}>
                    <Card>
                        <CardBody>
                            <header class="section-header">Thông tin người nhận</header>
                            <Show when={props.order}>
                                <LabelInformation label="Mã đơn hàng" value={props.order.orderID}/>
                                <LabelInformation label="Trạng thái đơn hàng">
                                    <Button color={OrderStatusLabelColor(props.order.status)}>
                                        {t(ORDER_STATE_LABEL[props.order.status])}
                                    </Button>
                                </LabelInformation>
                                <Show when={props.contract} fallback={
                                    <LabelInformation label="ID đơn vị thụ hưởng" value={props.order.receiverID}/>
                                }>
                                    <LabelInformation label="ID đơn vị thụ hưởng" value={props.order.receiverID}/>
                                    <LabelInformation label="Hợp đồng">
                                        <A href={`/contract/${props.contract.code}`}>
                                            {props.contract.name}
                                        </A>
                                    </LabelInformation>
                                </Show>
                                
                                <LabelInformation label="Thời gian đặt hàng" value={formatDatetime(props.order.createdTime)}/>
                                <LabelInformation label="Thời gian xác nhận" value={formatDatetime(props.order.confirmationDate)}/>
                                <Show when={props.order.cancelledTime}>
                                    <LabelInformation label="Thời gian huỷ đơn" value={formatDatetime(props.order.cancelledTime)}/>
                                </Show>
                                <hr/>
                                <LabelInformation label="Tên người nhận" value={props.order.receiverName}/>
                                <LabelInformation label="Số điện thoại người nhận" value={props.order.receiverPhoneNumber}/>
                                <LabelInformation label="Địa chỉ" value={props.order.receiverShippingAddress}/>
                                <LabelInformation label="Khu vực">
                                    <Show when={props.address}>
                                        {props.address.name}, {props.address.districtName}, {props.address.provinceName}
                                    </Show>
                                </LabelInformation>
                            </Show>
                        </CardBody>
                    </Card>
                </Col>
                <Col xs={12} md={6} lg={6}>
                    <Card style={{"height": "100%"}}>
                        <CardBody>
                            <header class="section-header">Thông tin giao hàng</header>
                            <Show when={props.order}>
                                <LabelInformation label="Thanh toán">
                                    <Show when={props.order.paymentMethod == "PAYMENT_METHOD_CREDIT"}>
                                        Công nợ
                                    </Show>
                                    <Show when={props.order.paymentMethod !== "PAYMENT_METHOD_CREDIT"}>
                                        {props.order.paymentMethod}
                                    </Show>
                                </LabelInformation>
                                <LabelInformation label="Hình thức vận chuyển">
                                    <Show when={props.order.deliverMethod == "DELIVERY_PLATFORM_NORMAL"}>
                                        Giao hàng tiêu chuẩn
                                    </Show>
                                    <Show when={props.order.deliverMethod !== "DELIVERY_PLATFORM_NORMAL"}>
                                        {props.order.deliverMethod}
                                    </Show>
                                </LabelInformation>
                                <LabelInformation label="Mã vận đơn" value={props.order.saleOrderCode}/>
                                <LabelInformation label="Trạng thái vận đơn" value="-"/>
                                <LabelInformation label="Nhà vận chuyển" value="-"/>
                                <LabelInformation label="Ngày giao dự kiến" value={formatDatetime(props.order.deliveredTime)}/>
                                <LabelInformation label="Ghi chú" value={props.order.note}/>
                            </Show>
                        </CardBody>
                    </Card>
                </Col>
            </Row>
            <br/>
            <Row class="row-gap-3">
                <Col xs={12} md={6} lg={6}>
                    <Card>
                        <CardBody>
                            <header class="section-header">Thông tin xuất hoá đơn</header>
                            <LabelInformation label="Tên công ty" value={props.order.invoice ? props.order.invoice.companyName : "-"}/>
                            <LabelInformation label="Mã số thuế" value={props.order.invoice ? props.order.invoice.taxCode : "-"}/>
                            <LabelInformation label="Địa chỉ công ty" value={props.order.invoice ? props.order.invoice.companyAddress : "-"}/>
                            <LabelInformation label="Email" value={props.order.invoice ? props.order.invoice.email : "-"}/>
                            <LabelInformation label="Yêu cầu xuất HĐ">
                                {props.order.canExportInvoice && props.order.canExportInvoice == true ? "Có" : "Không"}
                            </LabelInformation>
                            <hr/>
                            <InvoiceInfo 
                                invoiceNo={props.order.invoiceNo ? props.order.invoiceNo : "-"}
                                internalOrderID={props.order.orderID}
                            />
                        </CardBody>
                    </Card>
                </Col>
                <Col xs={12} md={6} lg={6}>
                    <Card style={{"height": "100%"}}>
                        <CardBody>
                            <header class="section-header">Thông tin thành tiền</header>
                            <Show when={props.order}>
                                <LabelInformation label="Tổng tiền đặt hàng" value={formatCurrency(props.order.totalAmount)}/>
                                <LabelInformation label="Tổng tiền" value={formatCurrency(props.order.actualAmount)}/>
                            </Show>
                        </CardBody>
                    </Card>
                </Col>
            </Row>
            <br/>
            <Row class="row-gap-3">
                <Col xs={12}>
                    <Card>
                        <CardBody>
                            <header class="section-header">Thông tin sản phẩm</header>
                            <Table responsive hover>
                                <TableHead>
                                    <TableRow>
                                        <TableHeaderCell></TableHeaderCell>
                                        <TableHeaderCell>STT</TableHeaderCell>
                                        <TableHeaderCell>SKU</TableHeaderCell>
                                        <TableHeaderCell>Sản phẩm</TableHeaderCell>
                                        <TableHeaderCell>Lot/Date</TableHeaderCell>
                                        <TableHeaderCell>
                                            <div style={{ "text-align": "right" }}>SL đặt</div>
                                        </TableHeaderCell>
                                        <TableHeaderCell>
                                            <div style={{ "text-align": "right" }}>SL giao</div>
                                        </TableHeaderCell>
                                        <TableHeaderCell>Đơn vị</TableHeaderCell>
                                        <TableHeaderCell>
                                            <div style={{ "text-align": "right" }}>Đơn giá</div>
                                        </TableHeaderCell>
                                        <TableHeaderCell>
                                            <div style={{ "text-align": "right" }}>Thành tiền</div>
                                        </TableHeaderCell>
                                        <TableHeaderCell>
                                            <div style={{ "text-align": "right" }}>Tổng</div>
                                        </TableHeaderCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                <Index
                                    each={props.items}
                                    fallback={
                                        <TableRow>
                                            <TableCell colSpan={100} style={{ "text-align": "center" }}>
                                                Không tìm thấy dữ liệu sản phẩm
                                            </TableCell>
                                        </TableRow>
                                    }
                                >
                                    {(item, index) => <OrderItemTableRow item={item()} index={index+1} product={props.productMap[item().lotID]}/>}
                                </Index>
                                </TableBody>
                            </Table>
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        </>
	);
}

function OrderItemTableRow(props) {
	const [isExpanded, setIsExpanded] = createSignal(false);
    const { t } = useTranslate();
    const toggleExpand = () => {
        setIsExpanded(!isExpanded());
    };

	return (
        <>
            <TableRow>
                <TableCell>
                    <Button
                        class="p-2"
                        variant="outline"
                        color="secondary"
                        onClick={toggleExpand} 
                        startIcon={isExpanded() ? <MdiArrowDown font-size="5"/> : <MdiArrowRight font-size="5"/>}
                    />
                </TableCell>
                <TableCell>{props.index}</TableCell>
                <TableCell>
                    <A href="#">
                        {props.item.sellerCode + "." + props.item.productCode}
                    </A>
                </TableCell>
                <TableCell>
                    <Show when={props.product} fallback={<p>[{props.item.productID}] - {props.item.productName}</p>}>
                        [{props.product.productID}] - {props.product.lotName}
                    </Show>
                </TableCell>
                <TableCell></TableCell>
                <TableCell>
                    <div style={{ "text-align": "right" }}>
                        {formatNumber(props.item.quantity)}
                    </div>
                </TableCell>
                <TableCell>
                    <div style={{ "text-align": "right" }}>
                        {formatNumber(props.item.completedQuantity)}
                    </div>
                </TableCell>
                <TableCell>{props.item.unitName ? props.item.unitName : props.item.unit}</TableCell>
                <TableCell>
                    <div style={{ "text-align": "right" }}>
                        {formatCurrency(props.item.price)}
                    </div>
                </TableCell>
                <TableCell>
                    <div style={{ "text-align": "right" }}>{formatCurrency(props.item.totalAmount)}</div>
                </TableCell>
                <TableCell>
                    <div style={{ "text-align": "right", "font-weight": "bold" }}>{formatCurrency(props.item.actualPrice)}</div>
                </TableCell>
            </TableRow>
            <Show when={isExpanded()}>
                <Index
                    each={props.item.completedInfos}
                    fallback={
                        <TableRow>
                            <TableCell colSpan={100} style={{ "text-align": "center" }}></TableCell>
                        </TableRow>
                    }
                >
                    {(item, index) => <OrderItemOutBoundRow item={item()} index={index+1}/>}
                </Index>
                
            </Show>
        </>
	);
}

function OrderItemOutBoundRow(props) {
    return (
        <TableRow>
            <TableCell colSpan={4}></TableCell>
            <TableCell style={{ "text-align": "center" }}>
                {props.item.lot ? props.item.lot : "-"}/{props.item.expDate ? props.item.expDate : "-"}
            </TableCell>
            <TableCell></TableCell>
            <TableCell>
                <div style={{ "text-align": "right" }}>
                    {formatNumber(props.item.quantity)}
                </div>
            </TableCell>
        </TableRow>
    );
}