export function isTrimable(str):boolean {
	if (!str || typeof str !== "string") return false;
	try {
		str.trim();
		return true;
	} catch (error) {
		return false;
	}
}

export function trimObjectValue<T>(obj: T): T {
	const result = {} as T;

	if (!obj) {
		return result;
	}

	Object.keys(obj).forEach((key) => {
		if (obj[key] && isTrimable(obj[key])) {
			result[key] = obj[key].trim();
		} else {
			result[key] = obj[key];
		}
	});

	return result;
}
