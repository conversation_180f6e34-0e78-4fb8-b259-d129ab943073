import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary, Show } from "solid-js";
import { createRouteData, redirect, useNavigate, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ContractAnnexFormCreate } from "~/containers/ContractAnnexFormCreate";
import { getBid } from "~/services/tender/bid.client";
import { createContractAnnex, getContract } from "~/services/tender/contract.client";
import { validateContractAnnexForm } from "~/services/tender/contract.model";


export function routeData({ location, params }) {
    return createRouteData(
		async ([query]) => {
            const contractCode = params.code;

			if (contractCode === "") {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}
            
            const res = await getContract({
				code: contractCode,
				option: {},
			});

			if (res.status !== API_STATUS.OK) {
				throw redirect("/404");
			}
			const contract = res.data?.[0];
			const bidID = contract.bidID;

			const resBid = await getBid({
				bidID,
				option: {},
			});
			if (resBid.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

			return {
				contract: contract,
				bid: resBid.data[0],
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="contract:add_contract"
			namespaces={["contract"]}
			breadcrumbs={[BREADCRUMB.CONTRACT, BREADCRUMB.ADD_CONTRACT]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 
    const getData = useRouteData();

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Thêm phụ lục hợp đồng</h1>
				</Col>
				<Col xs={12}>
                    <Show when={getData()}>
                        <ErrorBoundary fallback={ErrorMessage}>
                            <AddContractAnnex contract={getData()?.contract} bid={getData()?.bid}/>
                        </ErrorBoundary>
                    </Show>
				</Col>
			</Row>
		</div>
	);
}

function AddContractAnnex(props) {
    const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();
	
    const hookForm = createForm({
		initialValues: {
            products: [],
			attachments: [""],
			bidID: props.bid.bidID,
			isAppendProduct: false,
		},
		validate: (values) => validateContractAnnexForm(values, true, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
            const payload = data;

            payload.products.forEach((e) => {
                e.productID = +e.productID;
                e.lotID = +e.lotID;
            })
            payload.products = payload.products.filter((e) => e.lotID > 0 && e.productID > 0)
			payload.attachments = payload.attachments.filter((e) => e.length > 0);
			
			if(!payload.contractDate) {
				delete payload.contractDate;
			}

			if(!payload.expireDate) {
				delete payload.expireDate;
			}
			
            const res = await createContractAnnex(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create contract:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.create_success`);
			    navigate(`/contract/${props.contract.code}`, { replace: false });
            }

            return;
		},
	});

	return (
		<ContractAnnexFormCreate 
			hookForm={hookForm} 
			bid={props.bid}
			contractOptions={[{label: props.contract.name, value: props.contract.code}]}/>
	);
}