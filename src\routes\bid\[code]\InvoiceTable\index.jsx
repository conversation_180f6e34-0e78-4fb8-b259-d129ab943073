import { <PERSON><PERSON>, Card, Row, Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { Index } from "solid-js";
import BMTablePagination from "~/components/table/BMTablePagination";
import { LOT_STATUS_LABEL_COLOR } from "~/services/tender/lot.model";
import EditIcon from "~icons/mdi/square-edit-outline";

export function InvoiceTable(props) {
    const { t } = useTranslate();
    return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`bid:no`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.created_time`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.invoice_no`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:lot.name`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:lot.lot_no`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:lot.expire_date`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.quantity`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.price`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.vat`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.amount`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.attachment`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invoice.date_of_payment`}</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.data}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`bid:invoice.not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(invoice) => <InvoiceTableRow item={invoice()} />}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

function InvoiceTableRow(props) {
    const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.item.id}</TableCell>
			<TableCell>{props.item.createdTime}</TableCell>
			<TableCell>{props.item.code}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={t`bid:edit_bid`}>
						<Button
							class="p-2"
							variant="outline"
							color="secondary"
							startIcon={<EditIcon />}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}