import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	FormInput,
	FormSelect,
	Row,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { isEmptyObject } from "@buymed/solidjs-component/utils";
import { Match, Show, Switch, createMemo } from "solid-js";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import ConfirmModal from "~/components/ConfirmModal";
import { FORM_MODE } from "~/constants/buymed";
import { ACCOUNT_STATUS_OPTIONS } from "~/services/iam/account.model";
import SaveIcon from "~icons/mdi/content-save";
import { Tab, TabPanel, Tabs } from "@buymed/solidjs-component/components";
import { PageTabs } from "~/components/PageTabs";

/**
 * @param {object} props
 * @param {object} props.hookForm
 * @param {string=} props.mode - FORM_MODE
 * @returns {import("solid-js").JSXElement}
 */
export function CreateEditUserForm(props) {
	const { t } = useTranslate();

	function onChangeEmail(email) {
		let username = props.hookForm.data()["username"];
		// Auto set username if user has not input
		if (email && !username) {
			username = email.split("@")[0];
			props.hookForm.setFields("username", username, true);
		}
	}

	const tabs = createMemo(() => ["INFO", "DATA", "INVOICE"]);

	return (
		<Row class="gap-3">
			<Col xs={12}>

			</Col>
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
		</Row>
	);

	return (
		<form ref={props.hookForm.form}>
			<Card>
				<CardBody>
					<section class="basic-info-section d-flex flex-column row-gap-3">
						<header class="section-header">{t`user:basic_info`}</header>

						<Row class="row-gap-3">
							<Col xs={12} md={6} lg={4}>
								<FormInput
									name="email"
									id="email"
									label={t`user:email`}
									placeholder={t`user:email_placeholder`}
									onChange={(e) => onChangeEmail(e.target.value)}
									invalid={!isEmptyObject(props.hookForm.errors("email"))}
									feedbackInvalid={props.hookForm.errors("email")}
									disabled={props.mode === FORM_MODE.EDIT}
									required
								/>
							</Col>

							<Col xs={12} md={6} lg={4}>
								<FormInput
									name="username"
									id="username"
									label={t`user:username`}
									placeholder={t`user:username_placeholder`}
									invalid={!isEmptyObject(props.hookForm.errors("username"))}
									feedbackInvalid={props.hookForm.errors("username")}
									disabled={props.mode === FORM_MODE.EDIT}
									required
								/>
							</Col>

							<Show when={props.mode === FORM_MODE.CREATE}>
								<Col xs={12} md={6} lg={4}>
									<FormInput
										name="fullname"
										id="fullname"
										label={t`user:fullname`}
										placeholder={t`user:fullname_placeholder`}
										invalid={!isEmptyObject(props.hookForm.errors("fullname"))}
										feedbackInvalid={props.hookForm.errors("fullname")}
										required
									/>
								</Col>
							</Show>

							<Show when={props.mode === FORM_MODE.EDIT}>
								<Col xs={12} md={6} lg={4}>
									<FormSelect
										name="status"
										id="status"
										label={t`user:account_status`}
										options={ACCOUNT_STATUS_OPTIONS(t)}
									/>
								</Col>
							</Show>
						</Row>
					</section>
				</CardBody>
			</Card>

			<div class="submit-wrapper">
				<Switch>
					<Match when={props.mode === FORM_MODE.CREATE}>
						<Button
							type="submit"
							color="success"
							size="lg"
							loading={props.hookForm.isSubmitting()}
							startIcon={<SaveIcon />}
						>
							{t`common:button.save`}
						</Button>
					</Match>
					<Match when={props.mode === FORM_MODE.EDIT}>
						<AuthContent privilege={PRIVILEGE.USER.UPDATE}>
							<ConfirmModal
								onOK={props.hookForm.handleSubmit}
								trigger={(openModal) => (
									<Button
										type="submit"
										color="success"
										size="lg"
										startIcon={<SaveIcon />}
										onClick={(e) => {
											e.preventDefault();
											openModal();
										}}
									>
										{t`common:button.save`}
									</Button>
								)}
							>
								{t("user:confirm_request", {
									username: props.hookForm.data("username"),
								})}
							</ConfirmModal>
						</AuthContent>
					</Match>
				</Switch>
			</div>
		</form>
	);
}
