import { createForm } from "@felte/solid";
import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	DEFAULT_LIMIT,
	DEFAULT_PAGE,
	DatePicker,
	ErrorMessage,
	FormCheck,
	FormInput,
	FormSelect,
	Row,
	Tooltip,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, Show, createEffect, createMemo, createSignal } from "solid-js";
import { A, createRouteData, redirect, useNavigate, useParams, useRouteData, useSearchParams } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { FORM_MODE } from "~/constants/buymed";
import { getUser, updateUser } from "~/services/user/user.client";
import { validateUserForm } from "~/services/user/user.model";
import { CreateEditUserForm } from "../CreateEditUserForm";
import { getBid, getBidLotList } from "~/services/tender/bid.client";
import { PageTabs } from "~/components/PageTabs";
import { BID_CATEGORY, BID_CATEGORY_OPTIONS } from "~/services/tender/bid.model";
import { LotsTable } from "./LotsTable";
import styles from "./styles.module.css";
import { EditBidFormHeader } from "./EditBidFormHeader";
import { BeneficiaryTable } from "./BeneficiaryTable";
import { InvoiceTable } from "./InvoiceTable";

export function routeData({ location, params }) {
	// const params = useParams();
	return createRouteData(
		async ([query]) => {
			const bidID = params.code;

			if (!bidID) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

            const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			const res = await getBid({
				bidID,
				option: {
                    total: true,
                },
			});
			return {
				invoices: [],
				total: 0,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="bid:edit_bid"
			namespaces={["bid"]}
			breadcrumbs={[BREADCRUMB.BID, BREADCRUMB.EDIT_BID]}
		>
			<PageInvoiceList/>
		</AppLayout>
	);
};

function PageInvoiceList() {
	const { t } = useTranslate();
	const toast = useToast();
	const getData = useRouteData();

	return (
		<div class="mt-2">
			<Row class="gap-3">
                <EditBidFormHeader bidInfo={getData()}/>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<InvoiceTable data={getData()?.invoices} total={getData()?.total}/>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}