import { json, redirect } from "solid-start";
import { getAccessTokenByCode } from "~/services/iam/iam.client";

/**
 *
 * @param {object} param0
 * @param {Request} param0.request
 * @returns
 */
export async function GET({ request }) {
	const url = new URL(request.url);
	const code = url.searchParams.get("code");

	const res = await getAccessTokenByCode(request, code);
	if (res.status !== "OK") {
		return json(res);
	}
	const d = res.status === "OK" ? res.data[0] : null;

	const redirectURL = decodeURIComponent(url.searchParams.get("redirect") || "/");
	const headers = {
		// https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie
		"Set-Cookie": [
			`session_token=${d.accessToken}; Path=/; HttpOnly; Max-Age=99999999`,
			`refresh_token=${d.refreshToken}; Path=/sso/refresh; HttpOnly; Max-Age=99999999`,
		].join(","),
	};

	return redirect(redirectURL, { headers });
}
