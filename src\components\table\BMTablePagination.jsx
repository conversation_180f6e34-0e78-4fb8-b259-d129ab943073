import {
	DEFAULT_PAGE,
	ROW_PER_PAGES,
	TablePagination,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { Show } from "solid-js";
import { useSearchParams } from "solid-start/router";

export default function BMTablePagination(props) {
	const { t } = useTranslate();
	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<Show when={props.total}>
			<div class="px-3 py-2 border-top">
				<TablePagination
					size="sm"
					count={props.total}
					page={+searchParams.page || DEFAULT_PAGE}
					onPageChange={(page) =>
						setSearchParams({
							...searchParams,
							page: String(page),
							offset: undefined,
						})
					}
					onRowsPerPageChange={(limit) =>
						setSearchParams({
							...searchParams,
							limit: String(limit),
							page: undefined,
							offset: undefined,
						})
					}
					rowsPerPage={+searchParams.limit || ROW_PER_PAGES[0]}
					labelRowsPerPage={t`common:table.labelRowsPerPage`}
					labelDisplayedRows={(from, to, total) => (
						<span
							// eslint-disable-next-line solid/no-innerhtml
							innerHTML={t("common:table.labelDisplayedRows", {
								from,
								to,
								total,
							})}
						/>
					)}
					class="justify-content-end flex-wrap"
				/>
			</div>
		</Show>
	);
}
