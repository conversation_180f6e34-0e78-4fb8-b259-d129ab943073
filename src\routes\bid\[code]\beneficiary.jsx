import { createForm } from "@felte/solid";
import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	DEFAULT_LIMIT,
	DEFAULT_PAGE,
	DatePicker,
	ErrorMessage,
	FormCheck,
	FormInput,
	FormSelect,
	Row,
	Tooltip,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, Show, createEffect, createMemo, createSignal } from "solid-js";
import { A, createRouteData, redirect, useNavigate, useParams, useRouteData, useSearchParams } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { FORM_MODE } from "~/constants/buymed";
import { getUser, updateUser } from "~/services/user/user.client";
import { validateUserForm } from "~/services/user/user.model";
import { CreateEditUserForm } from "../CreateEditUserForm";
import { getBid, getBidLotList } from "~/services/tender/bid.client";
import { PageTabs } from "~/components/PageTabs";
import { BID_CATEGORY, BID_CATEGORY_OPTIONS } from "~/services/tender/bid.model";
import { LotsTable } from "./LotsTable";
import styles from "./styles.module.css";
import { EditBidFormHeader } from "./EditBidFormHeader";
import { BeneficiaryTable } from "./BeneficiaryTable";

export function routeData({ location, params }) {
	// const params = useParams();
	return createRouteData(
		async ([query]) => {
			const bidID = params.code;

			if (!bidID) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

            const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			const res = await getBidLotList({
				bidID,
				option: {
                    total: true,
                },
			});
			return {
				beneficiaries: [
                    {
                        id: 1,
                        name: "Trung tâm y tế Q1",
                        area: "TP Hồ Chí Minh"
                    },
                    {
                        id: 2,
                        name: "Trung tâm y tế Q2",
                        area: "TP Hồ Chí Minh"
                    },
                    {
                        id: 3,
                        name: "Trung tâm y tế Q3",
                        area: "TP Hồ Chí Minh"
                    },
                    {
                        id: 4,
                        name: "Trung tâm y tế TP Dĩ An",
                        area: "Tỉnh Bình Dương"
                    }
                ],
				total: res.total,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="bid:edit_bid"
			namespaces={["bid"]}
			breadcrumbs={[BREADCRUMB.BID, BREADCRUMB.EDIT_BID]}
		>
			<PageBeneficiaryList/>
		</AppLayout>
	);
};

function PageBeneficiaryList() {
	const { t } = useTranslate();
	const toast = useToast();
	const getData = useRouteData();

	return (
		<div class="mt-2">
			<Row class="gap-3">
                <EditBidFormHeader bidInfo={getData()}/>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<BeneficiaryTable data={getData()?.beneficiaries} total={getData()?.total}/>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}