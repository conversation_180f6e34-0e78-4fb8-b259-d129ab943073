{"root": true, "env": {"node": true, "commonjs": true}, "extends": ["eslint:recommended", "plugin:solid/recommended", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["prettier", "solid"], "rules": {"prettier/prettier": ["error", {"endOfLine": "auto", "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": false, "trailingComma": "es5"}]}, "overrides": [{"files": ["*.json"], "rules": {"indent": "off"}}]}