import { Contract } from "./contract.model";

export interface Product {
    code: string,
    lotID: number;
    bidNo: string;
    bidID: number;
    productID: number;
    contractID: number;
    contractType: string;

    name: string;
    price: number;
    priceUnit?: string; // VND
    guaranteeAmount?: number;
    groupMedicine: number; // number 1 to 9
    dosageForm?: string;
    packaging: string;
    unit?: string;
    registrationNo?: string;
    vendor: string;
    manufacturer: string;
    origin?: string;
    status: string;
    quantity: number;
    quantitySold?: number;
    quantityRemain?: number;
    note?: string;
    createdTime?: string;
	lastUpdatedTime?: string;
    contract?: Contract;
}

export const PRODUCT_STATUS = {
	WIN: "WIN",
    FAIL: "FAIL",
} as const;

export const PRODUCT_STATUS_LABEL = {
	[PRODUCT_STATUS.WIN]: "bid:status.WIN",
    [PRODUCT_STATUS.FAIL]: "bid:status.FAIL",
} as const;

export const PRODUCT_STATUS_LABEL_COLOR = {
	[PRODUCT_STATUS.WIN]: "success",
    [PRODUCT_STATUS.FAIL]: "warning",
} as const;

// ===================================================
// For QUERY
// ===================================================
export type ProductQuery = {};