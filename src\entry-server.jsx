import { create<PERSON><PERSON><PERSON>, renderAsync, StartServer } from "solid-start/entry-server";

// add middleware
export default createHandler(
	({ forward }) => {
		return async (event) => {
			let response = await forward(event);
			if (response.headers.get("content-type")?.includes("text/html")) {
				response.headers.set("content-type", "text/html; charset=utf-8");
			}
			return response;
		};
	},
	renderAsync((event) => <StartServer event={event} />)
);
