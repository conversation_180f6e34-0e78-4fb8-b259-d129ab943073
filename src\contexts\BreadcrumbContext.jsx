import { createContext, createSignal } from "solid-js";

export const BreadcrumbContext = createContext({
	/** @type {import("solid-js").Resource<any[]>} */
	breadcrumbs: [],

	/** @type {Function} */
	setBreadCrumbs: null,
});

export default function BreadcrumbProvider(props) {
	const [breadcrumbs, setBreadCrumbs] = createSignal([]);

	return (
		<BreadcrumbContext.Provider value={{ breadcrumbs, setBreadCrumbs }}>
			{props.children}
		</BreadcrumbContext.Provider>
	);
}
