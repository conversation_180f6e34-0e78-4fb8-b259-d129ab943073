import { Button, Tooltip, useToast, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createSignal } from "solid-js";
import { refetchRouteData, useNavigate } from "solid-start";
import ConfirmModal from "~/components/ConfirmModal";
import { cancelOrder } from "~/services/tender/order.client";


export function OrderCancelModal(props) {
	const { t } = useTranslate();
	const toast = useToast();
	const navigate = useNavigate();

	const [isSubmitting, setIsSubmitting] = createSignal(false);

	async function onCancelOrder(onClose) {
        if(!props.orderCode) {
            return;
        }
		setIsSubmitting(true);
        const orderCode = props.orderCode;
		const res = await cancelOrder({
            orderCode: orderCode,
        });

		setIsSubmitting(false);

		if (res.status !== API_STATUS.OK) {
			console.error("[Error] Update order:", res);
			toast.error(t("common:notify.action_fail", { error: res.message }));
		} else {
			toast.success(t("common:notify.action_success"));
			refetchRouteData()
		}
		if (onClose) {
			onClose();
		}
	}

	return (
		<ConfirmModal
			title="Xác nhận huỷ đơn hàng"
			trigger={(openModal) => (
				<Tooltip content={props.disabled ? "" : "Huỷ đơn hàng"}>
                    <Button 
						color={props.color} 
						class="m-3" onClick={props.disabled ? false : openModal}>
                        {props.buttonText}
                    </Button>
				</Tooltip>
			)}
			footer={(onClose) => (
				<div class="d-flex gap-2">
					<Button
						color="danger"
						onClick={() => onCancelOrder(onClose)}
						loading={isSubmitting()}
					>
                        Xác nhận
					</Button>
				</div>
			)}
		>
			<p>Xin hãy xác nhận để huỷ đơn hàng này, thay đổi này sẽ được áp dụng sau khi bạn xác nhận.</p>
			
		</ConfirmModal>
	);
}
