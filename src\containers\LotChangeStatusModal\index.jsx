import { Button, FormInput, Tooltip, useToast, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createSignal } from "solid-js";
import { refetchRouteData } from "solid-start";
import ConfirmModal from "~/components/ConfirmModal";
import { updateLot } from "~/services/tender/lot.client";
import { LOT_STATUS } from "~/services/tender/lot.model";


export function LotChangeStatusModal(props) {
	const { t } = useTranslate();
	const toast = useToast();

	const [isSubmitting, setIsSubmitting] = createSignal(false);
	const [note, setNote] = createSignal("");

	async function onUpdateLotStatus(status, onClose) {
        if(!props.lotID) {
            return;
        }
		setIsSubmitting(true);
        const lotID = +props.lotID;
		const res = await updateLot({
            lotID: lotID,
            status: status,
			note: note()
        });

		setIsSubmitting(false);

		if (res.status !== API_STATUS.OK) {
			console.error("[Error] Update lot:", res);
			toast.error(t("common:notify.action_fail", { error: res.message }));
		} else {
			toast.success(t("common:notify.action_success"));
		}

		handleModalClose();
		
		if (onClose) {
			onClose();
		}
		refetchRouteData();
	}

	function handleModalClose() {
        setNote(""); // Clear the note signal
    }

	return (
		<ConfirmModal
			title="Cập nhật trạng thái lô dự thầu"
			trigger={(openModal) => (
				<Tooltip content={props.disabled ? "" : t`common:tooltip.update`}>
                    <Button 
						color={props.color} 
						class="m-3" onClick={props.disabled ? false : openModal}>
                        {props.buttonText}
                    </Button>
				</Tooltip>
			)}
			footer={(onClose) => (
				<div class="d-flex gap-2">
					<Button
						color="danger"
						onClick={() => onUpdateLotStatus(LOT_STATUS.FAIL, onClose)}
						loading={isSubmitting()}
					>
                        FAIL
					</Button>
					<Button
						color="success"
						onClick={() => onUpdateLotStatus(LOT_STATUS.WIN, onClose)}
						loading={isSubmitting()}
					>
                        WIN
					</Button>
				</div>
			)}
		>
			<p>Xin hãy xác nhận để cập nhật trạng thái lô đấu thầu sản phẩm, thay đổi này sẽ được áp dụng sau khi bạn xác nhận.</p>
			<ul class="mb-0">
				<li>
				    WIN: <b>Đấu thầu lô thành công</b>
				</li>
				<li>
					FAIL: <b>Đấu thầu lô KHÔNG thành công</b>
				</li>
			</ul>
			<br/>
			<div>
				<FormInput
					name="note"
					placeholder="Nhập ghi chú"
					value={note()} // Bind the value to the signal
                    onInput={(e) => setNote(e.target.value)} // Update the signal on input change
				/>
			</div>
		</ConfirmModal>
	);
}
