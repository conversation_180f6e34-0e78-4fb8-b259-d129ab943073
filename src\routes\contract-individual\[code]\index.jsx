import {
	Col,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { Show } from "solid-js";
import { A, createRouteData, redirect, useLocation, useNavigate, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ContractFormCreate } from "~/containers/ContractFormCreate";
import { getContract, getContractAnnexList, updateContract } from "~/services/tender/contract.client";
import { validateContractForm } from "~/services/tender/contract.model";
import styles from "./styles.module.css";
import { ContractIndividualFormCreate } from "~/containers/ContractIndividualFormCreate";

export function routeData({ location }) {
	const params = useParams();
	return createRouteData(
		async ([code]) => {
			const contractCode = code;
			// const data = {};

			if (contractCode === "") {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			const [respContract, respAnnex] = await Promise.all([
				getContract({
					code: contractCode,
					option: {},
				}),
				getContractAnnexList({
					q: {
						contractCode
					},
					option: {},
				})
			]);

			
			if (respContract.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

			return {
				contract: respContract.data?.[0],
				annexes: respAnnex.data
			};
		},
		{
			key: () => {
				return [params.code];
			},
		}
	);
}

export default () => {
	const params = useParams();
	const getData = useRouteData();
	const location = useLocation();
	var patternContractProduct = /^\/contract\/[a-zA-Z0-9]+\/product$/;
	var patternContractInfo = /^\/contract\/[a-zA-Z0-9]+$/;

	return (
		<AppLayout
			pageTitle="contract:edit_contract"
			namespaces={["contract"]}
			breadcrumbs={[
				BREADCRUMB.CONTRACT,
				{
					label: "Cập nhật hợp đồng",
					link: `/contract-individual/${params.code}`,
				}
			]}
		>
			<Show when={getData()}>
				<Row class="gap-3">
					<Col xs={12}>
						<div class="d-flex align-items-center justify-content-between">
							<h1 class="page-title">Thông tin hợp đồng</h1>
						</div>
					</Col>
					<Col xs={12}>
						<A 
							href={`/contract-individual/${params.code}`}
							classList={{
								[styles["link"]]: true,
								[styles["active"]]: patternContractInfo.test(location.pathname),
							}}
							class={styles["link"]}
						>Thông tin</A>
						{/* <AuthContent privilege={PRIVILEGE.BID.VIEW_LOT}> */}
							<A 
								href={`/contract-individual/${params.code}/product`}
								classList={{
									[styles["link"]]: true,
									[styles["active"]]: patternContractProduct.test(location.pathname),
								}}
								class={styles["link"]}
							>Sản phẩm</A>
						{/* </AuthContent> */}
					</Col>
				</Row>
				<br/>
                <PageEditContract contractInfo={getData()?.contract} annexList={getData()?.annexes}/>
            </Show>
		</AppLayout>
	);
};

function PageEditContract(props) {
	const params = useParams();
	const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();

	const hookForm = createForm({
		initialValues: {
			name: props.contractInfo.name,
			contractNumber: props.contractInfo.contractNumber,
			beneficiaryName: props.contractInfo.beneficiaryName,
			address: props.contractInfo.address,
			phoneNumber: props.contractInfo.phoneNumber,
			taxCode: props.contractInfo.taxCode,
			contractType: props.contractInfo.contractType,
			contractValue: props.contractInfo.contractValue,
			startTime: props.contractInfo.startTime,
			endTime: props.contractInfo.endTime,
			signingDate: props.contractInfo.signingDate,
			expireDate: props.contractInfo.expireDate,
			status: props.contractInfo.status,
			attachments: props.contractInfo.attachments || [""],
			extendAttachments: props.contractInfo.extendAttachments || [""],
			isStoreContractDocument: props.contractInfo.isStoreContractDocument || false
		},
		validate: (values) => validateContractForm(values, false, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
			const code = params.code
            
			// remove field if empty
			if(!data.signingDate) {
				delete data.signingDate;
			}

			const res = await updateContract({...data, code});
			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Update contract:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.update_success`);
			    navigate(`/contract/${code}`, { replace: false });
            }
            return;
		},
	});

	return (
		<ContractIndividualFormCreate 
			hookForm={hookForm} 
			annexes={props.annexList}
			contract={props.contractInfo}/>
	);
}