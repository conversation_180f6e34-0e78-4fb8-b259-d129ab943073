import { <PERSON><PERSON>, Col, FormInput, Row, useTranslate } from "@buymed/solidjs-component/components";
import { Show } from "solid-js";
import { A, useLocation } from "solid-start";
import styles from "./styles.module.css";
import AddIcon from "~icons/mdi/plus";
import { BID_STATUS } from "~/services/tender/bid.model";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";

export function EditBidFormHeader(props) {
    const { t } = useTranslate();
	const location = useLocation();

	const paths = () => location.pathname.split("/");
	const curPath = paths()[paths().length - 1];

    return (
        
        <Row class="gap-3">
            <Show when={props.bidInfo}>
                <Col xs={12}>
                    <div class="d-flex align-items-center justify-content-between">
                        <h1 class="page-title">Cập nhật g<PERSON><PERSON> thầu</h1>
                    </div>
				</Col>
                <Col xs={12}>
                    <A 
                        href={`../info`}
                        classList={{
                            [styles["link"]]: true,
                            [styles["active"]]: curPath === "info",
                        }}
                        class={styles["link"]}
					>Thông tin</A>
                    <AuthContent privilege={PRIVILEGE.BID.VIEW_LOT}>
                        <A 
                            href={`../lot`}
                            classList={{
                                [styles["link"]]: true,
                                [styles["active"]]: curPath === "lot",
                            }}
                            class={styles["link"]}
                        >Sản phẩm</A>
                    </AuthContent>
                </Col>
                {/* <Show when={props.tab === "lot"}>
                    <Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
                        <Button
                            color={props.bidInfo.bid.status === BID_STATUS.WAITING ? "success" : "second"}
                            href={`../lot/new`}
                            disabled={props.bidInfo.bid.status !== BID_STATUS.WAITING}
                            startIcon={<AddIcon class="fs-4" />}
                        >
                            {t`bid:add_lot`}
                        </Button>
                    </Col>
                </Show> */}
            </Show>
        </Row>
    );
}