{"bib": "Bid", "bid_list": "List of bidding packages", "no": "No", "invitaion_of_bid": "Invitation of Bid (IB)", "procuring_entity": "Procuring Entity", "main_category": "Category", "bid_open_date": "Open date", "bid_closing_date": "Closing date", "number_product": "Number product", "bid_price": "Bid price", "winning_bid_price": "Winning Bid Price", "bid_status": "Status", "winning_bid_decision": "Winning bid decision", "contract_number": "Contract number", "bid_quantity": "Bid quantity", "winning_bid_quantity": "Winning bid quantity", "not_found": "Bidding packages not found", "created_time": "Date", "enter_procuring_entity": "", "enter_invitation_of_bid": "", "edit_bid": "Edit bid", "add_bid": "Add bid", "status": {"WIN": "WIN", "CANCEL": "CANCEL", "WAITING": "WAITING", "all": "All"}}