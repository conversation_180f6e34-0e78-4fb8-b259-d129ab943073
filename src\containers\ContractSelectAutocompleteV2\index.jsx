import { FormAutocomplete, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS, debounce, sortBy } from "@buymed/solidjs-component/utils";
import { createResource, createSignal, splitProps } from "solid-js";
import { getContractList } from "~/services/tender/contract.client";

export function parseContractOption(contract) {
	return {
		value: contract.code,
		label: `[${contract.contractNumber}] - ${contract.name} - ${contract.beneficiaryName}`,
		data: {
			id: contract.contractID,
			code: contract.code,
			name: contract.name,
			beneficiaryName: contract.beneficiaryName,
			contractNumber: contract.contractNumber,
			address: contract.address,
			phoneNumber: contract.phoneNumber,
			bidID: contract.bidID,
			mainCategory: contract.mainCategory
		},
	};
}

export function ContractSelectAutocompleteV2(props) {
	const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);

	const { t } = useTranslate();
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);

	const q = {};
	if (props.status) {
		q.status = props.status;
	}

	const [contractMapOptions] = createResource(
		search,
		async (search) => {
			const res = await getContractList(
				{
					q,
					search,
					offset: 0,
					limit: 20,
				}
			);

			if (res.status !== API_STATUS.OK) {
				console.error("[Error] fetch contractMapOptions", res);
				return [];
			}

			let options = res.data.map((bid) => parseContractOption(bid));

			if (search === "" && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					options.push(lastOption());
				}
			}

			options = sortBy(options, "label");
			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	function onInputChange(e) {
		setSearch(e.target.value);
	}
	const debouncedOnInputChange = debounce(onInputChange, 500);

	return (
		<FormAutocomplete
			name={local.name}
			options={contractMapOptions()}
			label={t`common:contract`}
			placeholder={t`common:contract_search`}
			onInputChange={debouncedOnInputChange}
			isLoading={contractMapOptions.loading}
			// renderOption={(props, { data }) => (
			// 	<li {...props}>
			// 		[{data.id}] - <b>{data.name}</b>
			// 	</li>
			// )}
			onChange={(e) => {
				setLastOption(e);
				props.onChange(e)
			}}
			{...other}
		/>
	);
}
