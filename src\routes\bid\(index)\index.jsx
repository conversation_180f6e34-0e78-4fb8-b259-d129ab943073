import {
	<PERSON>ton,
	Col,
	DEFAULT_LIMIT,
	DEFAULT_PAGE,
	ErrorMessage,
	Row,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, createEffect, createResource, createSignal } from "solid-js";
import { createRouteData, redirect, useRouteData } from "solid-start";
import { useLocation, useSearchParams } from "solid-start/router";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getUserList } from "~/services/user/user.client";
import { USER_STATUS } from "~/services/user/user.model";
import AddIcon from "~icons/mdi/plus";
import { BidsFormFilter } from "~/containers/BidsFormFilter";
import { BidsTable } from "~/containers/BidsTable";
import { getBidList } from "~/services/tender/bid.client";
import { BID_STATUS } from "~/services/tender/bid.model";
import MdiMicrosoftExcel from "~icons/mdi/microsoft-excel";


export function routeData({ location }) {
	return createRouteData(
		async ([query]) => {
			const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			// Prepare query data
			if (q.bidID) {
				q.bidID = +q.bidID;
			}

			// TODO: Fix <DateRangePicker> to allow customizing name, so we can use createdFrom, not createdTimeStartDate
			if (q.createdTimeStartDate) {
				q.createdFrom = q.createdTimeStartDate;
			}
			if (q.createdTimeEndDate) {
				q.createdTo = q.createdTimeEndDate;
			}

			const tab = query.tab === undefined ? 1 : +query.tab
			switch (tab) {
				case 1: {
					q.status = BID_STATUS.WIN;
					break;
				}
				case 2: {
					q.status = BID_STATUS.WAITING
					break;
				}
				case 3: {
					q.status = BID_STATUS.FAIL
					break;
				}
				case 4: {
					q.status = BID_STATUS.EXPIRED
					break;
				}
			}

			const res = await getBidList({
				q,
				offset,
				limit,
				option: {
					total: true,
				},
			});

			return {
				bids: res.data,
				total: res.total,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout namespaces={["bid"]} pageTitle="bid:bid_list" breadcrumbs={[BREADCRUMB.BID]}>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const pageData = useRouteData();
	const [searchParams] = useSearchParams();
	const { t } = useTranslate();
	const location = useLocation();

	const [tabs] = createResource(
		// Fetch the count for tabs
		// We only need re-fetch when q change (i.e. submit filter), when tab change
		// no need to re-fetch
		() => toQueryObject(searchParams).q || "{}",
		async (qString) => {
			const q = JSON.parse(qString);

			// TODO: Fix <DateRangePicker> to allow customizing name, so we can use createdFrom, not createdTimeStartDate
			if (q.createdTimeStartDate) {
				q.createdFrom = q.createdTimeStartDate;
			}
			if (q.createdTimeEndDate) {
				q.createdTo = q.createdTimeEndDate;
			}

			const totalRes = await Promise.all([
				getBidList({
					q,
					limit: 1,
					option: { total: true },
				}),
				getBidList({
					q: { ...q, status: BID_STATUS.WIN },
					limit: 1,
					option: { total: true },
				}),
				getBidList({
					q: { ...q, status: BID_STATUS.WAITING },
					limit: 1,
					option: { total: true },
				}),
				getBidList({
					q: { ...q, status: BID_STATUS.FAIL },
					limit: 1,
					option: { total: true },
				}),
				getBidList({
					q: { ...q, status: BID_STATUS.EXPIRED },
					limit: 1,
					option: { total: true },
				}),
			]);
			const totals = totalRes.map((res) => res.total || 0);

			return [
				t`bid:status.all` + ` (${totals[0]})`,
				t`bid:status.WIN` + ` (${totals[1]})`,
				t`bid:status.WAITING` + ` (${totals[2]})`,
				t`bid:status.FAIL` + ` (${totals[3]})`,
				t`bid:status.EXPIRED` + ` (${totals[4]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<div class="d-flex align-items-center justify-content-between">
					<h1 class="page-title">{t`bid:bid_list`}</h1>

					<AuthContent privilege={PRIVILEGE.BID.CREATE}>
						<Button
							color="success"
							href="/bid/new"
							startIcon={<AddIcon class="fs-4" />}
						>
							{t`bid:add_bid`}
						</Button>
					</AuthContent>
				</div>
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<BidsFormFilter />
				</ErrorBoundary>
			</Col>
			<Col xs={12}>
				<PageTabs tabs={tabs()} defaultTab={1}/>
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<BidsTable bids={pageData()?.bids} total={pageData()?.total} />
				</ErrorBoundary>
			</Col>
		</Row>
	);
}
