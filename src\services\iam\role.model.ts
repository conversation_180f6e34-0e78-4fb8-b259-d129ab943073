import { ACCOUNT_TYPE } from "./account.model";

// Role represent a role of user of the system
export interface Role {
	lastUpdatedTime: string;
	createdTime: string;
	code: string;

	// contain clientId (represent one app)
	appID: number;
	name: string;
	description: string;

	// permission codes
	permissions: string[];
	assignableRoles: string[];
	type: keyof typeof ACCOUNT_TYPE;
	maxDevice: number;
}
