import { API_STATUS } from "@buymed/solidjs-component/src/utils/common";
import { getProductMapList } from "../product/product-map.client";

export const CONTRACT_TYPE = {
	DEBT: "DEBT",
    ANNEX: "ANNEX",
} as const;

export const CONTRACT_TYPE_LABEL = {
	[CONTRACT_TYPE.DEBT]: "contract:type.DEBT",
    [CONTRACT_TYPE.ANNEX]: "contract:type.ANNEX",
} as const;

export const CONTRACT_TYPE_OPTIONS = (t) => [
    { value: CONTRACT_TYPE.DEBT, label: t(CONTRACT_TYPE_LABEL[CONTRACT_TYPE.DEBT]) },
    { value: CONTRACT_TYPE.ANNEX, label: t(CONTRACT_TYPE_LABEL[CONTRACT_TYPE.ANNEX]) },
];

export interface Contract {
    contractID: number;
    code: string;
    contractNumber: string;
    name: string;
    contractType: string;
    contractValue: number;
    currentAmount: number;
    debtLimit: number;
    beneficiaryName: string;
    invitationOfBid: string;
    mainContractCode?: string;
    bidID: number;
    bidName: string;
    decision?: string;
    taxCode: string;
    phoneNumber: string;
    termOfPayment: number;
    numOfProduct: number;
    documentFiles: Array<string>;
    isStoreContractDocument: boolean;
    address?: string;
    startTime: string;
    endTime: string;
    signingDate?: string;
    expireDate?: string;
    status: string;
    content?: string;
    note?: string;
    createdTime?: string;
	lastUpdatedTime?: string;
    debt?: Debt;
}

export interface Debt {
    documentDataCode: string;
    totalBalanceTemporary: number;
    totalDebtTemporary: number;
}
export interface ContractDetail {
    productID: number;
    productCode: string;
    vendor: string;
    price: number;
    quantity: number;
}

export const CONTRACT_STATUS = {
    ACTIVE: "ACTIVE", // Đang thực hiện
    FINISH: "FINISH", // HD hết hiệu lực
    DONE: "DONE", // HD hoàn tất có biên bản thanh lý
    DRAFT: "DRAFT",
} as const;

export const CONTRACT_STATUS_LABEL = {
    [CONTRACT_STATUS.ACTIVE]: "contract:status.ACTIVE",
    [CONTRACT_STATUS.FINISH]: "contract:status.FINISH",
    [CONTRACT_STATUS.DONE]: "contract:status.DONE",
    [CONTRACT_STATUS.DRAFT]: "contract:status.DRAFT"
} as const;

export const CONTRACT_STATUS_LABEL_COLOR = {
    [CONTRACT_STATUS.ACTIVE]: "success",
    [CONTRACT_STATUS.DRAFT]: "warning"
} as const;

export const CONTRACT_STATUS_OPTIONS = (t) => [
    { value: CONTRACT_STATUS.ACTIVE, label: t(CONTRACT_STATUS_LABEL[CONTRACT_STATUS.ACTIVE]) },
    { value: CONTRACT_STATUS.DONE, label: t(CONTRACT_STATUS_LABEL[CONTRACT_STATUS.DONE]) },
    { value: CONTRACT_STATUS.FINISH, label: t(CONTRACT_STATUS_LABEL[CONTRACT_STATUS.FINISH]) },
    { value: CONTRACT_STATUS.DRAFT, label: t(CONTRACT_STATUS_LABEL[CONTRACT_STATUS.DRAFT]) },
];

export const PRODUCT_CONTRACT_TYPE = {
	MAIN_CONTRACT: "MAIN_CONTRACT",
    CONTRACT_ANNEX: "CONTRACT_ANNEX",
} as const;

// ===================================================
// Validation
// ===================================================
export async function validateContractForm(values, isAnnex, t) {
	const err: any = {};

    if (!values.bidID) {
        err.bidID = t`contract:bid_id_is_required`;
    }

    if (!values.contractNumber || values.contractNumber.length == 0) {
        err.contractNumber = t`contract:contract_is_required`;
    }

    if (!values.beneficiaryName || values.beneficiaryName.length == 0) {
        err.beneficiaryName = t`contract:beneficiary_name_is_required`;
    }

    if (!values.name || values.name.length == 0) {
        err.name = t`contract:name_is_required`;
    }

    if (!values.address || values.address.length == 0) {
        err.address = t`contract:address_is_required`;
    }

    if (!values.phoneNumber || values.phoneNumber.length == 0) {
        err.phoneNumber = t`contract:phone_number_is_required`;
    }

    if (!values.taxCode || values.taxCode.length == 0) {
        err.taxCode = t`contract:tax_is_required`;
    }

    if (isAnnex == false) {
        if (!values.debtLimit || (values.debtLimit <= 0 || values.debtLimit > 1e11)) {
            err.debtLimit = t`contract:contract_value_is_greater_than_zero_less_than_1e11`;
        }
    } else {
        if (values.contractValue && (values.contractValue <= 0 || values.contractValue > 1e11)) {
            err.contractValue = t`contract:contract_value_is_greater_than_zero_less_than_1e11`;
        }
    }
    
    if (values.startTime && values.endTime && values.startTime >= values.endTime) {
        err.endTime = t`contract:end_date_is_greater_than_start_date`;
    }

    if (!values.debtLimit || values.debtLimit.length <= 0) {
        err.debtLimit = t`contract:debt_limit_is_required`;
    }

    if (!values.paymentTerm || values.paymentTerm.length <= 0) {
        err.paymentTerm = t`contract:pay_term_is_required`;
    }

    // console.log(err);
    
	return err;
}

export function validateContractAnnexForm(values, isAnnex, t) {
	const err: any = {};

    if (!values.annexNumber || values.annexNumber.length == 0) {
        err.annexNumber = t`contract:contract_is_required`;
    }

    if (!values.name || values.name.length == 0) {
        err.name = t`contract:name_is_required`;
    }

    if (!values.content || values.content.length == 0) {
        err.content = t`contract:content_is_required`;
    }
    
	return err;
}