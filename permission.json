[{"app_id": 1016, "code": "TENDER_VIEW_BID_LIST", "name": "Tender - View list bids", "apis": ["QUERY/tender/core-tender/v1/bid/list"], "screens": ["/bid"]}, {"app_id": 1016, "code": "TENDER_CREATE_BID_INFO", "name": "Tender - Create bid information", "apis": ["POST/tender/core-tender/v1/bid"], "screens": ["/bid/new"]}, {"app_id": 1016, "code": "TENDER_VIEW_BID_INFO", "name": "Tender - View detail bid information", "apis": ["GET/tender/core-tender/v1/bid/info"], "screens": ["/bid/[code]/info"]}, {"app_id": 1016, "code": "TENDER_VIEW_LOT_LIST", "name": "Tender - View list lots of bid", "apis": ["GET/tender/core-tender/v1/bid/info", "QUERY/tender/core-tender/v1/lot/list"], "screens": ["/bid/[code]/lot"]}, {"app_id": 1016, "code": "TENDER_UPDATE_LOT_STATUS", "name": "Tender - Update lot status", "apis": ["PUT/tender/core-tender/v1/lot"], "screens": ["/bid/[code]/lot"]}, {"app_id": 1016, "code": "TENDER_UPDATE_BID_INFO", "name": "Tender - Update bid information", "apis": ["PUT/tender/core-tender/v1/bid/info"], "screens": ["/bid/[code]/info"]}, {"app_id": 1016, "code": "TENDER_CREATE_LOT_INFO", "name": "Tender - Create lot", "apis": ["GET/tender/core-tender/v1/bid/info", "POST/tender/core-tender/v1/lot"], "screens": ["/bid/[code]/lot/new"]}, {"app_id": 1016, "code": "TENDER_VIEW_CONTRACT_LIST", "name": "Tender - View list contracts", "apis": ["QUERY/tender/core-tender/v1/contract/list"], "screens": ["/contract"]}, {"app_id": 1016, "code": "TENDER_CREATE_CONTRACT", "name": "Tender - Create contract information", "apis": ["QUERY/tender/core-tender/v1/bid/list", "POST/storage/document/v1/document/resumable-upload", "PUT/storage/document/v1/document/resumable-upload", "POST/tender/core-tender/v1/contract", "QUERY/tender/core-tender/v1/lot/list", "POST/tender/core-tender/v1/product-component/fuzzy"], "screens": ["/contract/new"]}, {"app_id": 1016, "code": "TENDER_UPDATE_CONTRACT", "name": "Tender - Update contract information", "apis": ["QUERY/tender/core-tender/v1/bid/list", "POST/storage/document/v1/document/resumable-upload", "PUT/storage/document/v1/document/resumable-upload", "PUT/tender/core-tender/v1/contract"], "screens": ["/contract/[code]"]}, {"app_id": 1016, "code": "TENDER_VIEW_PRODUCT_LIST", "name": "Tender - View list product of contract", "apis": ["POST/tender/core-tender/v1/product-component/fuzzy", "QUERY/tender/core-tender/v1/product/list", "GET/tender/core-tender/v1/product"], "screens": ["/product"]}, {"app_id": 1016, "code": "TENDER_UPDATE_PRODUCT_INFO", "name": "Tender - Update product information", "apis": ["POST/tender/core-tender/v1/product-component/fuzzy", "GET/tender/core-tender/v1/product", "GET/tender/core-tender/v1/lot", "QUERY/tender/core-tender/v1/lot/list", "PUT/tender/core-tender/v1/product"], "screens": ["/product/info"]}, {"app_id": 1016, "code": "TENDER_EXPORT_LOT_LIST", "name": "Tender - Export lots of bid", "apis": ["GET/tender/core-tender/v1/bid/info", "QUERY/tender/core-tender/v1/lot/list"], "screens": ["/bid/[code]/lot"]}, {"app_id": 1016, "code": "TENDER_VIEW_CONTRACT", "name": "Tender - View contract information", "apis": ["GET/tender/core-tender/v1/contract", "QUERY/tender/core-tender/v1/bid/list"], "screens": ["/contract/[code]"]}, {"app_id": 1016, "code": "TENDER_VIEW_LOT_INFO", "name": "Tender - View lot information", "apis": ["GET/tender/core-tender/v1/lot"], "screens": ["/bid/[code]/lot/edit"]}, {"app_id": 1016, "code": "TENDER_VIEW_CONTRACT_ANNEX_LIST", "name": "Tender - View list annexes of the contract", "apis": ["QUERY/tender/core-tender/v1/contract-annex/list"], "screens": ["/contract/[code]/contract-annex"]}, {"app_id": 1016, "code": "TENDER_VIEW_ORDER_LIST", "name": "Tender - View list order", "apis": ["QUERY/tender/core-tender/v1/order/list"], "screens": ["/order"]}, {"app_id": 1016, "code": "TENDER_CREATE_ORDER", "name": "Tender - Create internal order", "apis": ["POST/tender/core-tender/v1/order", "GET/tender/core-tender/v1/order"], "screens": ["/order/new"]}, {"app_id": 1016, "code": "TENDER_UPDATE_ORDER", "name": "Tender - Update order information", "apis": ["PUT/tender/core-tender/v1/order", "PUT/tender/core-tender/v1/order/status", "GET/tender/core-tender/v1/order"], "screens": ["/order/[code]"]}, {"app_id": 1016, "code": "TENDER_CONVERT_PRODUCT_UNIT", "name": "Tender - Convert product unit", "apis": ["POST/tender/core-tender/v1/product/unit-convert", "QUERY/tender/core-tender/v1/product/unit-convert"], "screens": ["/contract/new", "/bid/[code]/lot/new"]}, {"app_id": 1016, "code": "TENDER_CREATE_PRODUCT_INFO", "name": "Tender - Create product for contract", "apis": ["POST/tender/core-tender/v1/product"], "screens": ["/product/new"]}, {"app_id": 1016, "code": "TENDER_VIEW_BENEFICIARY_LIST", "name": "Tender - View list beneficiaries", "apis": ["QUERY/tender/core-tender/v1/beneficiary/list"], "screens": ["/beneficiary"]}, {"app_id": 1016, "code": "TENDER_REMOVE_ORDER", "name": "Tender - Remove order", "apis": ["DELETE/tender/core-tender/v1/order"], "screens": ["/order/[code]"]}, {"app_id": 1016, "code": "TENDER_VIEW_INVOICE", "name": "Tender - View invoice information", "apis": ["GET/oms/order/v1/invoice"], "screens": ["/order/[code]"]}, {"app_id": 1016, "code": "TENDER_CANCEL_ORDER", "name": "Tender - Request cancel order", "apis": ["PUT/tender/core-tender/v1/cancel-order"], "screens": ["/order/[code]"]}]