import {
    <PERSON><PERSON>,
    Col,
    DEFAULT_LIMIT,
    DEFAULT_PAGE,
    ErrorMessage,
    Row,
    useToast,
    useTranslate
} from "@buymed/solidjs-component/components";
import { toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, Show, createEffect, createResource, createSignal } from "solid-js";
import { createRouteData, useLocation, useParams, useRouteData, useSearchParams } from "solid-start";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { OrderFormFilter } from "~/containers/OrderFormFilter";
import { OrderTable } from "~/containers/OrderTable";
import { ProductFormFilter } from "~/containers/ProductFormFilter";
import { ProductTable } from "~/containers/ProductTable";
import { getProductMapList } from "~/services/product/product-map.client";
import { getContractList } from "~/services/tender/contract.client";
import { getOrderList } from "~/services/tender/order.client";
import { getProductList } from "~/services/tender/product.client";
import AddIcon from "~icons/mdi/plus";

export function routeData({ location, params }) {
	return createRouteData(
		async ([query]) => {
			// Prepare query data
            const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			if(q.orderID) {
				q.orderID = +q.orderID;
			}
			
			switch (query.tab) {
				case "1": {
					q.status = "WAIT_TO_CONFIRM";
					break;
				}
				case "2": {
					q.status = "CONFIRMED"
					break;
				}
				case "3": {
					q.status = "PROCESSING"
					break;
				}
				case "4": {
					q.status = "WAIT_TO_DELIVER"
					break;
				}
				case "5": {
					q.status = "DELIVERING"
					break;
				}
				case "6": {
					q.status = "DELIVERED"
					break;
				}
				case "7": {
					q.status = "COMPLETED"
					break;
				}
			}

			const resOrder = await getOrderList({
				q,
                offset,
                limit,
				option: {
                    total: true,
                },
			});

			let contractCodes = [];
			(resOrder.data || [])?.forEach((order) => {
				contractCodes = contractCodes.concat(order.contractCode || []);
			});
			contractCodes = Array.from(new Set(contractCodes));
			const contractMap = {};

			if (contractCodes.length > 0) {
				const resContractList = await getContractList({
                    q: {contractCodes},
                    offset: 0,
                    limit: 1000,
                    option: {}
                });
				(resContractList.data || [])?.forEach((contract) => {
					contractMap[contract.code] = contract;
				});
			}

			return {
				orders: resOrder.data,
				contractMap: contractMap,
				total: resOrder.total,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	const params = useParams();
	return (
		<AppLayout
			pageTitle="Danh sách đơn hàng"
			namespaces={["order"]}
			breadcrumbs={[
				BREADCRUMB.ORDER
			]}
		>
			<PageListOrder/>
		</AppLayout>
	);
};

function PageListOrder() {
	const { t } = useTranslate();
	const getData = useRouteData();
	const location = useLocation();
	const [searchParams] = useSearchParams();
	const [tab, setTab] = createSignal(+location.query.tab || 0);

	const [tabs] = createResource(
		// Fetch the count for tabs
		// We only need re-fetch when q change (i.e. submit filter), when tab change
		// no need to re-fetch
		() => toQueryObject(searchParams).q || "{}",
		async (qString) => {
			const q = JSON.parse(qString);

			if(q.orderID) {
				q.orderID = +q.orderID;
			}
			
			const totalRes = await Promise.all([
				getOrderList({
					q,
					limit: 1,
					option: { total: true },
				}),
				getOrderList({
					q: { ...q, status: "WAIT_TO_CONFIRM" },
					limit: 1,
					option: { total: true },
				}),
				getOrderList({
					q: { ...q, status: "CONFIRMED" },
					limit: 1,
					option: { total: true },
				}),
				getOrderList({
					q: { ...q, status: "PROCESSING" },
					limit: 1,
					option: { total: true },
				}),
				getOrderList({
					q: { ...q, status: "WAIT_TO_DELIVER" },
					limit: 1,
					option: { total: true },
				}),
				getOrderList({
					q: { ...q, status: "DELIVERING" },
					limit: 1,
					option: { total: true },
				}),
				getOrderList({
					q: { ...q, status: "DELIVERED" },
					limit: 1,
					option: { total: true },
				}),
				getOrderList({
					q: { ...q, status: "COMPLETED" },
					limit: 1,
					option: { total: true },
				}),
			]);
			const totals = totalRes.map((res) => res.total || 0);

			return [
				t`Tất cả` + ` (${totals[0]})`,
				t`Chờ xác nhận` + ` (${totals[1]})`,
				t`Đã xác nhận` + ` (${totals[2]})`,
				t`Đang xử lý` + ` (${totals[3]})`,
				t`Chờ giao hàng` + ` (${totals[4]})`,
				t`Đang giao hàng` + ` (${totals[5]})`,
				t`Đã giao hàng` + ` (${totals[6]})`,
				t`Đã hoàn tất` + ` (${totals[7]})`,
			];
		}
	);

	createEffect(() => {
		setTab(+location.query.tab);
	});
	
	return (
		<div class="mt-2">
			<Show when={getData()}>
				<Row class="gap-3">
					<Col xs={12}>
						<div class="d-flex align-items-center justify-content-between">
							<h1 class="page-title">Danh sách đơn hàng</h1>

							<Button
								color="success"
								href="/order/new"
								startIcon={<AddIcon class="fs-4" />}
							>
								{t`order:add_order`}
							</Button>
						</div>
					</Col>
					<Col xs={12}>
						<ErrorBoundary fallback={ErrorMessage}>
							<OrderFormFilter/>
						</ErrorBoundary>
					</Col>
					<Col xs={12}>
						<PageTabs tabs={tabs()} />
					</Col>
					<Col xs={12}>
                        <ErrorBoundary fallback={ErrorMessage}>
                            <OrderTable 
                                orders={getData()?.orders} 
								contractMap={getData()?.contractMap}
								tab={tab()}
                                total={getData()?.total}
                            />
                        </ErrorBoundary>
					</Col>
				</Row>
			</Show>
		</div>
	);
}