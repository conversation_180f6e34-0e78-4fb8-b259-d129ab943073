import {
	<PERSON><PERSON>,
	Card,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
	Tooltip,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { A } from "@solidjs/router";
import { Index } from "solid-js";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import BMTablePagination from "~/components/table/BMTablePagination";
import { BID_CATEGORY_LABEL, BID_STATUS_LABEL_COLOR } from "~/services/tender/bid.model";
import EditIcon from "~icons/mdi/square-edit-outline";

/**
 * @param {object} props
 * @param {import("~/services/tender/bid.model").Bid[]} props.bids
 * @param {number} props.total
 * @returns {import("solid-js").JSXElement}
 */
export function BidsTable(props) {
	const { t } = useTranslate();

	return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`bid:no`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:invitation_of_bid`}</TableHeaderCell>
						<TableHeaderCell class="col-2">{t`bid:bid`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:main_category`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:bid_opening_date`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:bid_closing_date`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:bid_quantity`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:bid_price`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:winning_bid_quantity`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:winning_bid_price`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:winning_bid_decision`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:contract_number`}</TableHeaderCell>
						<TableHeaderCell>{t`bid:bid_status`}</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.bids}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`bid:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(bid) => <BidTableRow item={bid()} />}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 *
 * @param {object} props
 * @param {import("~/services/tender/bid.model").Bid} props.item
 * @returns {import("solid-js").JSXElement}
 */
function BidTableRow(props) {
	const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.item.bidID}</TableCell>
			<TableCell>
				<A href={`/bid/${props.item.bidID}/info`}>
					{props.item.itb}
				</A>
			</TableCell>
			<TableCell>
				<b>{props.item.bidName}</b>
				<br/>
				{props.item.procuringEntity}
			</TableCell>
			<TableCell>{BID_CATEGORY_LABEL[props.item.mainCategory]}</TableCell>
			<TableCell>{formatDatetime(props.item.openingDate, "dd/MM/yyyy")}</TableCell>
			<TableCell>{formatDatetime(props.item.closingDate, "dd/MM/yyyy")}</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.numOfProduct)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.bidPrice)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.winningQuantity)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.winningPrice)}
				</div>
			</TableCell>
			<TableCell>{props.item.winningDecision}</TableCell>
			<TableCell>{props.item.contractNo}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Button color={BID_STATUS_LABEL_COLOR[props.item.status]} class="m-3">
						{props.item.status}
					</Button>
				</div>
			</TableCell>
			<TableCell>
				<AuthContent privilege={PRIVILEGE.BID.VIEW}>
					<div class="d-flex justify-content-center align-items-center gap-1">
						<Tooltip content={t`bid:edit_bid`}>
							<Button
								class="p-2"
								variant="outline"
								color="primary"
								startIcon={<EditIcon />}
								href={`/bid/${props.item.bidID}/info`}
							/>
						</Tooltip>
					</div>
				</AuthContent>
			</TableCell>
		</TableRow>
	);
}
