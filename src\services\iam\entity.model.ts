import { QueryInput } from "@buymed/solidjs-component/services";
import { Role } from "./role.model";

export const EntityStatus = {
	NEW: "NEW",
	ACTIVE: "ACTIVE",
	INACTIVE: "INACTIVE",
	CLOSED: "CLOSED",
} as const;

export const EntityType = {
	Branch: "BRANCH",
	Department: "DEPARTMENT",
} as const;

export interface Entity {
	lastUpdatedTime: string;
	createdTime: string;

	entityID: number;
	code: string;
	name: string;
	description: string;

	orgID: number;
	appID: number;

	status: keyof typeof EntityStatus;
	type: keyof typeof EntityType;

	contactInfo: ContactInfo;
	planInfo: PlanInfo;

	// Ex: "scope:customerId" : 100
	// "scope:sellerId":
	// "department": [list department]
	// Dùng để authorize ABAC cho entity
	attributes: Record<string, any>;

	roleList: string[];
	tag: string[];
	address: string;

	// FOR APP ENTITY
	departmentCode: string;
	branch: string;

	// for cache
	cacheTime: string;

	// For display
	department: Entity;
	totalMember: number;
	roleInfoList: Role[];

	// For create entity
	accountID: number;

	// Tự đăng ký app cho department với current app ID
	registerAppID: number;
}

export interface ContactInfo {
	address: string;
	countryCode: string;
	provinceCode: string;
	districtCode: string;
	wardCode: string;

	phoneNumber: string;
	email: string;
}

export interface PlanInfo {
	registryRecordID: number;
}

// For QUERY
export type GetEntityList = {
	offset?: number;
	limit?: number;
	appID?: number;
	orgID?: number;
	getTotal?: boolean;
	search?: string;
};
