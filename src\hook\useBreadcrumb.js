import { createEffect, useContext } from "solid-js";
import { BreadcrumbContext } from "~/contexts/BreadcrumbContext";

/**
 * Create a custom hook in order to create breadcrumb
 * @param {*} breadcrumbs:  Breadcrumb[] -> List of breadcrumb
 */
export function useBreadcrumb(breadcrumbs) {
	const { setBreadCrumbs } = useContext(BreadcrumbContext);

	createEffect(() => {
		setBreadCrumbs(breadcrumbs);
	});
}
