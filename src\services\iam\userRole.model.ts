import { Department } from "../department/department.model";
import { Entity } from "./entity.model";

// UserRole represent a role of user of the system
export interface UserRole {
	lastUpdatedTime: string;
	createdTime: string;
	appID: number;
	orgID: number;
	accountID: number;
	roleCode: string;

	entityID: number;

	departmentCode: string;
	department: Department;
	subDepartmentCodes: string[];

	// for action source
	replacementRole?: string;
	entity?: Entity;
	roleName?: string;

	isMain: boolean;

	username?: string;
}
