import { Entity } from "./entity.model";
import { Role } from "./role.model";
import { UserJobTitle } from "./userJobTitle.model";
import { UserRole } from "./userRole.model";

export const ACCOUNT_STATUS = {
	ACTIVE: "ACTIVE",
	INACTIVE: "INACTIVE",
} as const;

export const ACCOUNT_STATUS_LABEL = {
	[ACCOUNT_STATUS.ACTIVE]: "common:status.ACTIVE",
	[ACCOUNT_STATUS.INACTIVE]: "common:status.INACTIVE",
} as const;

export const ACCOUNT_STATUS_OPTIONS = (t) => [
	{ value: ACCOUNT_STATUS.ACTIVE, label: t(ACCOUNT_STATUS_LABEL[ACCOUNT_STATUS.ACTIVE]) },
	{ value: ACCOUNT_STATUS.INACTIVE, label: t(ACCOUNT_STATUS_LABEL[ACCOUNT_STATUS.INACTIVE]) },
];

export const GENDER = {
	MALE: "MALE",
	FEMALE: "FEMALE",
	OTHER: "OTHER",
} as const;

export const ACCOUNT_TYPE = {
	USER: "USER",
	SYSTEM: "SYSTEM",
} as const;

export interface Account {
	// Generic fields
	id: string;
	createdTime: string;
	lastUpdatedTime: string;

	// Account info
	accountID: number;
	username: string;

	email: string;
	emailVerified: boolean;

	phoneNumber: string;
	phoneNumberVerified: boolean;
	countryCode: string;

	status: keyof typeof ACCOUNT_STATUS;

	// Basic info
	fullname: string;
	firstName: string;
	lastName: string;
	dayOfBirth: string;
	gender: keyof typeof GENDER;
	avatarUrl: string;
	locale: string;

	// Private info
	// For temporary password
	isTemporaryPassword: boolean;
	tempPasswordExpiration: string;

	// Note and info
	bannedReason: string;
	isBanned: boolean;

	logs: string[];

	// Cache data
	orgIDs: number[];
	appIDs: number[];
	entityIDs: number[];

	attributes: Record<string, any>;
	label: string[];
	type: keyof typeof ACCOUNT_TYPE;
	source: string;
	createdBy: number;

	// for login block
	failedLoginCount: number;
	failedLoginTime: string;
	remainingLogin: number;

	// For tracking
	loginTime: string;
	lastActive: string;

	// user role
	userRole: UserRole;

	// ForQuery
	createdFrom: string;
	createdTo: string;
	appID: number;
	entityID: number;

	// For display me
	orgID: number;
	userRoles: UserRole[];
	linkedAccountID: number;

	userJobTitles: UserJobTitle[];

	// for cache
	cacheTime: string;

	branch: string;
	userBranchInfos: UserBranchInfo[];
}

export interface UserBranchInfo {
	branch: string;

	branchAccountID: number;

	// SSO AccountID của quản lý
	managerAccountID: number;

	userRoles: UserRole[];
	userJobTitles: UserJobTitle[];
}

export type Remote = {
	forwardedFor: string;
	ip: string;
	userAgent: string;
};

export type Session = {
	accountID: number;
	token: string;
	tokenType: string;

	appID: number;
	appCode: string;
	orgID: number;
	entityID: number;
	clientID: string;

	userAgent: string;

	metadata: Record<string, any>;
	expiredTime: string;

	// For SSO
	sessionType: string;
	authUser: number;

	// For impersonate token only
	rootAccountID: number;

	// For flow code
	guestRole: string;
};

export interface LinkedAccount {
	// Generic fields
	id: string;
	createdTime: string;
	lastUpdatedTime: string;

	// Account info
	accountID: number;

	username: string;
	linkedAccountID: number;

	type: string;
	appID: number;

	// for cache
	cacheTime: string;
}

export type ActionSource = {
	// Account info
	account: Account;
	remote: Remote;
	session: Session;
	userRoles: UserRole[];

	// used for /me API only
	roles: Role[];
	apis: string[];
	screens: string[];
	entities: Entity[];

	// Linked account
	linkedAccount: LinkedAccount;
};

export type LoginSessionInfo = {
	accountId: number;
	fullname: string;
	username: string;
	email: string;
	phoneNumber: string;
	countryCode: string;
	avatarUrl: string;

	status: keyof typeof ACCOUNT_STATUS;

	sessionStatus: string; // ACTIVE | EXPIRED
	authUser: number;
};
