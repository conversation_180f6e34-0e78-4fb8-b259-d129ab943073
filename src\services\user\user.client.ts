import { APIResponse, QueryInput } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";
import { ResetPasswordInfo } from "../iam/iam.model";
import { QueryOption, ResetUserAccountPasswordInput, User, UserQuery } from "./user.model";

const URL = "/demo/user/v1";

/** Return a list of users  */
export async function getUserList(
	input?: QueryInput<UserQuery, QueryOption>
): Promise<APIResponse<User>> {
	// return callAPI(HTTP_METHOD.QUERY, `${URL}/user/list`, {
	// 	...input,
	// 	search: input.search ? String(input.search) : undefined,
	// });

	// Mock response
	return {
		status: "OK",
		message: "Query user successfully.",
		data: [
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 7654321,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user1",
				gender: "MALE",
			},
			{
				createdTime: "2024-02-27T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011872,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-27T09:39:43.113Z",
				status: "ACTIVE",
				username: "user2",
				gender: "MALE",
			},
			{
				createdTime: "2024-01-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011873,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-01-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user3",
				gender: "FEMALE",
			},
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011874,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user4",
				gender: "MALE",
			},
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011875,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "INACTIVE",
				username: "user5",
				gender: "FEMALE",
			},
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011876,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user6",
				gender: "MALE",
			},
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011877,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user7",
				gender: "MALE",
			},
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011878,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user8",
				gender: "FEMALE",
			},
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011879,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "INACTIVE",
				username: "user9",
				gender: "FEMALE",
			},
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 1011810,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "INACTIVE",
				username: "user10",
				gender: "MALE",
			},
		],
		total: 123,
	};
}

/** Return a single user */
export async function getUser({
	username,
	accountID,
	option,
}: {
	username?: string;
	accountID?: number;
	option?: QueryOption;
}): Promise<APIResponse<User>> {
	// return callAPI(HTTP_METHOD.GET, `${URL}/user`, {
	// 	username,
	// 	accountID,
	// 	option: option ? Object.keys(option).join(",") : undefined,
	// });

	// Mock response
	return {
		status: "OK",
		message: "Query user successfully.",
		data: [
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 7654321,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user1",
				gender: "MALE",
			}
		]
	};
}

/** Create user */
export async function createUser(data: User): Promise<APIResponse<User>> {
	// return callAPI(HTTP_METHOD.POST, `${URL}/user`, data);

	// Mock response
	return {
		status: "OK",
		message: "Created user successfully.",
		data: [
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 7654321,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user1",
				gender: "MALE",
				tempPassword: "123456",
				...data,
			}
		]
	};
}

/** Update user */
export async function updateUser(data: User): Promise<APIResponse<User>> {
	// return callAPI(HTTP_METHOD.PUT, `${URL}/user`, data);

	// Mock response
	return {
		status: "OK",
		message: "Updated user successfully.",
		data: [
			{
				createdTime: "2024-02-26T09:39:43.113Z",
				email: "<EMAIL>",
				accountID: 7654321,
				fullname: "<EMAIL>",
				lastUpdatedTime: "2024-02-26T09:39:43.113Z",
				status: "ACTIVE",
				username: "user1",
				gender: "MALE",
				...data,
			}
		]
	};
}

/** Reset password for a user */
export async function resetPassword(
	input: ResetUserAccountPasswordInput
): Promise<APIResponse<ResetPasswordInfo>> {
	// return callAPI(HTTP_METHOD.PUT, `${URL}/user/reset-password`, input);

	// Mock response
	return {
		status: "OK",
		data: [
			{
				password: "!@#$%RTGd",
			},
		],
		message: "Reset password successfully",
	};
}
