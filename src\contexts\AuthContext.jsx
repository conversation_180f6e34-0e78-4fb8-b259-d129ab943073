// @ts-nocheck
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createContext, createEffect, createSignal, onCleanup, useContext } from "solid-js";
import { createStore } from "solid-js/store";
import { isServer } from "solid-js/web";
import { createRouteData, redirect, useLocation, useNavigate } from "solid-start";
import { useRequest } from "solid-start/server";
import { BUYMED_VN_ENTITY_CODE, BUYMED_VN_ENTITY_ID } from "~/constants/buymed";
import { getLoggedInAccounts, getMe, postAuthorizeFromApp } from "~/services/iam/iam.client";
import { getSSOLoginUri } from "~/services/iam/path";

export function getAuthUser(path = "") {
	let pathname = path;
	if (isServer) {
		const e = useRequest();
		const url = new URL(e.request.url);
		pathname = url.pathname;
	} else {
		pathname = window.location.pathname;
	}

	let uid = 0;
	if (pathname?.startsWith("/u")) {
		const paths = pathname.replace("/u", "").split("/");
		uid = +paths[0];
	}

	return uid;
}

export const ACCOUNT_KEY = "account";
export const LOGGED_IN_ACCOUNT_KEY = "logged_in_account";

export const AuthContext = createContext({
	/** @type {import("solid-js").Resource<import("@buymed/solidjs-component/src/services/iam/account.model").Account>} */
	account: null,

	/** @type {Function} */
	validateScreen: () => Boolean,

	/** @type {import("solid-js").Accessor<import("~/services/iam/account.model").ActionSource>} */
	userInfo: null,

	/** @type {import("solid-js").Accessor<string>}*/
	documentToken: null,

	/** @type {import("solid-js").Resource<import("~/services/iam/account.model").LoginSessionInfo[]>} */
	loggedInAccounts: null,

	/** @type {import("solid-js/store").Store<BranchData>} */
	branchData: null,

	/** @type {import("solid-js/store").SetStoreFunction<BranchData>} */
	setBranchData: () => null,
});

const pathNoAuth = [
	"/403",
	"/404",
	"/500",
	"/login",
	"/register",
	"/forgot-password",
	"/reset-password",
];

export default function AuthProvider(props) {
	let meBroadcastChannel = null;
	const location = useLocation();
	const [userInfo, setUserInfo] = createSignal(null);
	const [documentToken, setDocumentToken] = createSignal(null);

	const [branchData, setBranchData] = createStore({
		orgID: 0,
		entityID: BUYMED_VN_ENTITY_ID,
		branch: BUYMED_VN_ENTITY_CODE,
		entityOptions: [],
	});

	function validatePermissionScreen(screensRequire) {
		const pathName = location.pathname;
		const screens = (userInfo() || {}).screens || [];

		// const screens = ["=/app", "/app/[code]", "/app/[code]/info"];
		// const screens = ["=/app"];
		if (!screens.includes("/") && !pathNoAuth.includes(pathName)) {
			// validate one path /app/create
			if (screensRequire?.length > 0 && !screens.includes(screensRequire[0])) {
				return false;
			}

			// filter
			// nếu permission có = thì cần chính xác
			// nếu permision không có = thì startsWith
			// =/app -> chỉ vào list /app
			// /app -> vào dc /app/*
			const urlCanAccess = screens.filter((item) => {
				const itemPaths = item.split("/");
				const currentPaths = pathName.split("/");

				if (item?.startsWith("=")) {
					return item.substring(1) === pathName;
				}
				if (itemPaths.length !== currentPaths.length) return false;

				for (let i = 0; i < currentPaths.length; i++) {
					if (
						itemPaths[i] !== currentPaths[i] &&
						(!itemPaths[i]?.startsWith("[") || !itemPaths[i]?.endsWith("]"))
					) {
						return false;
					}
				}
				return true;
			});

			if (urlCanAccess.length === 0) {
				return false;
			}
		}
		return true;
	}

	// validateScreen use in client ( useNavigate )
	function validateScreen(screensRequire) {
		const validate = validatePermissionScreen(screensRequire);
		// if (!validate) {
		// 	const navigate = useNavigate();
		// 	navigate("/403?pathAuth=" + location.pathname);
		// 	return false;
		// }
		return true;
	}

	const account = createRouteData(
		async () => {
			const uid = getAuthUser();

			/** @type {import("@buymed/solidjs-component/src/services/iam/account.model").ActionSource} */
			let me = userInfo();
			if (!me) {
				const res = await getMe(uid, { getEntities: true, getPermission: true });
				me = res.status === API_STATUS.OK ? res.data[0] : null;
			}
			if (!me) {
				throw redirect(getSSOLoginUri());
			}

			setUserInfo(me);
			setBranchData({
				orgID: me.userRoles?.[0]?.orgID,
				entityID: me.userRoles?.[0]?.entityID,
				branch: me.userRoles?.[0]?.entity?.code,
			});

			// get document token for open preview document
			const oathDocumentResp = await postAuthorizeFromApp();
			setDocumentToken(oathDocumentResp?.data?.[0]?.accessToken);

			if (me?.account) {
				return me.account;
			}

			throw redirect(getSSOLoginUri());
		},
		{ key: [ACCOUNT_KEY] }
	);

	const loggedInAccounts = createRouteData(
		async () => {
			const res = await getLoggedInAccounts();
			return res.status === API_STATUS.OK ? res.data : [];
		},
		{ key: [LOGGED_IN_ACCOUNT_KEY] }
	);

	createEffect(() => {
		if (userInfo()?.session?.clientID) {
			meBroadcastChannel = new BroadcastChannel(userInfo().session.clientID);

			meBroadcastChannel.onmessage = (e) => {
				if (
					e.data.clientID === userInfo().session.clientID &&
					e.data.accountID !== userInfo().session.accountID
				) {
					window.location.reload();
				}
			};

			// Broadcast the change to other tabs
			meBroadcastChannel.postMessage({
				accountID: userInfo().account.accountID,
				clientID: userInfo().session.clientID,
			});
		}
	});
	onCleanup(() => {
		meBroadcastChannel?.close();
	});

	return (
		<AuthContext.Provider
			value={{
				account,
				validateScreen,
				userInfo,
				loggedInAccounts,
				documentToken,
				branchData,
				setBranchData,
			}}
		>
			{props.children}
		</AuthContext.Provider>
	);
}

export function useAuth() {
	const context = useContext(AuthContext);

	if (!context) {
		throw new Error("[JS-Common]: useAuth must be used within a `<AuthProvider />` component");
	}

	return context;
}
