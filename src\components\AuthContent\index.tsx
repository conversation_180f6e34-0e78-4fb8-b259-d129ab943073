import { JSX, Show, createMemo, useContext } from "solid-js";
import { AuthContext } from "~/contexts/AuthContext";
import { ActionSource } from "~/services/iam/account.model";

export const PRIVILEGE = {
	USER: {
		CREATE: "ADD_USER",
		VIEW: "VIEW_USER",
		UPDATE: "EDIT_USER",
		RESET_PASSWORD: "RESET_PASSWORD_USER",
		VIEW_HISTORY: "VIEW_USER_HISTORY",
	},
	BID: {
		CREATE: "TENDER_CREATE_BID_INFO",
		VIEW: "TENDER_VIEW_BID_INFO",
		UPDATE: "TENDER_UPDATE_BID_INFO",
		VIEW_HISTORY: "VIEW_BID_HISTORY",
		VIEW_LOT: "TENDER_VIEW_LOT_LIST",
		EXPORT_LOT: "TENDER_EXPORT_LOT_LIST",
		CREATE_LOT: "TENDER_CREATE_LOT_INFO"
	},
	CONTRACT: {
		CREATE: "TENDER_CREATE_CONTRACT",
		VIEW: "TENDER_VIEW_CONTRACT",
		VIEW_PRODUCTS: "TENDER_VIEW_PRODUCT_LIST"
	},
	ORDER: {
		CREATE: "TENDER_CREATE_BID_INFO"
	}
} as const;

/**
 * check privilege exist in permission in roles
 */
export function hasPermission(userInfo: ActionSource, privilege: string) {
	return (userInfo?.roles || []).some(
		(role) =>
			(role.permissions || []).includes(privilege) ||
			(role.permissions || []).includes("ROOT") || 
			(role.permissions || []).includes("ALL")
	);
}

export interface AuthContentProps extends JSX.HTMLAttributes<HTMLDivElement> {
	/** The privilege to check */
	privilege: string;

	/** The components to render when the user does not have the privilege */
	fallback?: JSX.Element;
}

/**
 * The wrapper for Authorized content
 * @example
 * ```js
 * <AuthContent privilege="create:user" fallback={<Button disabled>Create</Button>}>
 * 		<Button onClick={() => {}}>Create</Button>
 * </AuthContent>
 * ```
 */
export default function AuthContent(props: AuthContentProps): JSX.Element {
	const { userInfo } = useContext(AuthContext);

	const hasPrivilege = createMemo(() => {
		return hasPermission(userInfo(), props.privilege);
	});

	return (
		<Show when={hasPrivilege()} fallback={props.fallback}>
			{props.children}
		</Show>
	);
}
