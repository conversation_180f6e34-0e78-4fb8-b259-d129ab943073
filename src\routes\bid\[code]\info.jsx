import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, Show } from "solid-js";
import { createRouteData, redirect, useNavigate, useParams, useRouteData, useSearchParams } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { getBid } from "~/services/tender/bid.client";
import { BidInformation } from "./BidInformation";
import { EditBidFormHeader } from "./EditBidFormHeader";


export function routeData({ location }) {
	const params = useParams();
	return createRouteData(
		async ([code]) => {
			const bidID = +code;
			// const data = {};

			if (!bidID) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			const res = await getBid({
				bidID,
				option: {},
			});
			if (res.status === API_STATUS.NOT_FOUND) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}
			if (res.status !== API_STATUS.OK) {
				// TODO: Error handling for res.status !== OK
				return {};
			}

			return res.data?.[0]; // {}
		},
		{
			key: () => {
				return [params.code];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="bid:edit_bid"
			namespaces={["bid"]}
			breadcrumbs={[BREADCRUMB.BID, BREADCRUMB.EDIT_BID]}
		>
			<PageBidInfo/>
		</AppLayout>
	);
};

function PageBidInfo() {
	const getData = useRouteData();

	return (
		<div class="mt-2">
			<Show when={getData()}>
				<Row class="gap-3">
					<EditBidFormHeader bidInfo={getData()}/>
					<Col xs={12}>
						<ErrorBoundary fallback={ErrorMessage}>
							<BidInformation data={getData()}/>
						</ErrorBoundary>
					</Col>
				</Row>
			</Show>
		</div>
	);
}