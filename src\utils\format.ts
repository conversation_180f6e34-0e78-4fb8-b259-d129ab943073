export function convertUTCDateToLocalDate(date) {
	date.setMinutes(date.getMinutes() - date.getTimezoneOffset());
	return date;
}

export function formatDateYYYYMMDDHHIISS(value) {
	const result2 = new Date(value).toLocaleString("en-GB", {
		hour12: false,
	});
	return result2;
}

export function formatEllipsis(text, maxLength) {
	if (text.length > maxLength) {
	  return text.substring(0, maxLength) + "...";
	}
	return text;
}

export function getCDNImageURL(img) {
	if(!img || img.length == 0) {
		return "";
	}
	return img.replace(import.meta.env.VITE_DOMAIN_GG_IMAGE, import.meta.env.VITE_DOMAIN_CDN_IMAGE);
}