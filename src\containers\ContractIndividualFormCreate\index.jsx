import { <PERSON><PERSON>, Card, CardBody, Col, DatePicker, EHTMLType, FileInput, FormCheck, FormInput, FormLabel, FormSelect, Row, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatCurrency, formatNumber, isEmptyObject } from "@buymed/solidjs-component/utils";
import { Index, Show, createEffect } from "solid-js";
import { useParams } from "solid-start";
import { useAuth } from "~/contexts/AuthContext";
import { CONTRACT_STATUS_OPTIONS, CONTRACT_TYPE_OPTIONS } from "~/services/tender/contract.model";
import { UNIT_OPTIONS } from "~/services/tender/lot.model";
import SaveIcon from "~icons/mdi/content-save";
import MdiMinusBox from '~icons/mdi/minus-box';
import MdiPlusBox from '~icons/mdi/plus-box';
import { ContractAnnexTable } from "../ContractAnnexTable";
import { ProductSelectAutocomplete } from "../ProductSelectAutocomplete";

export function ContractIndividualFormCreate(props) {
    const { t } = useTranslate();
    const params = useParams();
    const { documentToken } = useAuth();

    createEffect(() => {
        console.log("props.data= ",props.hookForm.data())
    })

    return (
        <Row class="gap-3">
            <form ref={props.hookForm.form}>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="contractNumber"
                                        id="contractNumber"
                                        label="Mã hợp đồng"
                                        disabled={props.contract}
                                        placeholder="Nhập mã hợp đồng"
                                        invalid={!isEmptyObject(props.hookForm.errors("contractNumber"))}
                                        feedbackInvalid={props.hookForm.errors("contractNumber")}
                                        required
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="name"
                                        id="name"
                                        label="Tên hợp đồng"
                                        placeholder="Nhập tên hợp đồng"
                                        invalid={!isEmptyObject(props.hookForm.errors("name"))}
                                        feedbackInvalid={props.hookForm.errors("name")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="beneficiaryName"
                                        id="beneficiaryName"
                                        label="Đơn vị thụ hưởng"
                                        placeholder="Nhập tên đơn vị/tổ chức"
                                        invalid={!isEmptyObject(props.hookForm.errors("beneficiaryName"))}
                                        feedbackInvalid={props.hookForm.errors("beneficiaryName")}
                                        required
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="address"
                                        id="address"
                                        label="Nhập địa chỉ"
                                        invalid={!isEmptyObject(props.hookForm.errors("address"))}
                                        feedbackInvalid={props.hookForm.errors("address")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="phoneNumber"
                                        id="phoneNumber"
                                        label="Số điện thoại"
                                        invalid={!isEmptyObject(props.hookForm.errors("phoneNumber"))}
                                        feedbackInvalid={props.hookForm.errors("phoneNumber")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="taxCode"
                                        id="taxCode"
                                        label="Mã số thuế"
                                        invalid={!isEmptyObject(props.hookForm.errors("taxCode"))}
                                        feedbackInvalid={props.hookForm.errors("taxCode")}
                                        required
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br />
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Nội dung</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="contractType"
                                        id="contractType"
                                        label="Loại hợp đồng"
                                        options={CONTRACT_TYPE_OPTIONS(t)}
                                        invalid={!isEmptyObject(props.hookForm.errors("contractType"))}
                                        feedbackInvalid={props.hookForm.errors("contractType")}
                                        disabled
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="contractValue"
                                        id="contractValue"
                                        type="number"
                                        label="Giá trị HD"
                                        invalid={!isEmptyObject(props.hookForm.errors("contractValue"))}
                                        feedbackInvalid={props.hookForm.errors("contractValue")}
                                        required
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
                                        label={t`contract:start_time`}
                                        name="startTime"
                                        id="startTime"
                                        placeholder="Chọn"
                                        invalid={!isEmptyObject(props.hookForm.errors("startTime"))}
                                        feedbackInvalid={props.hookForm.errors("startTime")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
                                        label={t`contract:end_time`}
                                        name="endTime"
                                        id="endTime"
                                        placeholder="Chọn"
                                        invalid={!isEmptyObject(props.hookForm.errors("endTime"))}
                                        feedbackInvalid={props.hookForm.errors("endTime")}
                                        required
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
                                        label={t`contract:signing_date`}
                                        name="signingDate"
                                        id="signingDate"
                                        placeholder="Chọn"
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
                                        label={t`contract:expire_date`}
                                        name="expireDate"
                                        id="expireDate"
                                        placeholder="Chọn"
                                        invalid={!isEmptyObject(props.hookForm.errors("expireDate"))}
                                        feedbackInvalid={props.hookForm.errors("expireDate")}
                                        required
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Hợp đồng</header>
                            <Row class="row-gap-3">
                                <Col xs={12} >
                                    <Index each={props.hookForm.data("attachments")}>
                                        {(attachment, index) => (
                                            <FileInput
                                                documentToken={documentToken()}
                                                mode={EHTMLType.Documents}
                                                type={EHTMLType.Documents}
                                                extensions=".xlsx, .xls, .doc, .docx, .ppt, .pptx, .pdf"
                                                onAdd={(newFile) => {
                                                    console.log("newFile=", newFile)
                                                    props.hookForm.setData("attachments", [
                                                        newFile.previewLink,
                                                        ...props.hookForm.data("attachments"),
                                                    ]);
                                                }}
                                                onRemove={() =>
                                                    props.hookForm.setData(
                                                        "attachments",
                                                        props.hookForm.data("attachments")?.filter(
                                                            (_, i) => i !== index
                                                        )
                                                    )
                                                }
                                                value={attachment}
                                            />
                                        )}
                                    </Index>
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br />
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Tài liệu đính kèm</header>
                            <Row class="row-gap-3">
                                <Col xs={12} >
                                    <Index each={props.hookForm.data("extendAttachments")}>
                                        {(attachment, index) => (
                                            <FileInput
                                                documentToken={documentToken()}
                                                mode={EHTMLType.Documents}
                                                type={EHTMLType.Documents}
                                                extensions=".xlsx, .xls, .doc, .docx, .ppt, .pptx, .pdf"
                                                onAdd={(newFile) => {
                                                    console.log("newFile=", newFile)
                                                    props.hookForm.setData("extendAttachments", [
                                                        newFile.previewLink,
                                                        ...props.hookForm.data("extendAttachments"),
                                                    ]);
                                                }}
                                                onRemove={() =>
                                                    props.hookForm.setData(
                                                        "extendAttachments",
                                                        props.hookForm.data("extendAttachments")?.filter(
                                                            (_, i) => i !== index
                                                        )
                                                    )
                                                }
                                                value={attachment}
                                            />
                                        )}
                                    </Index>
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Show when={props.contract}>
                    <br />
                    <Card>
                        <CardBody>
                            <section class="d-flex flex-column row-gap-3">
                                <header class="section-header">Trạng thái hợp đồng</header>
                                <Row class="row-gap-3">
                                    <Col xs={12} md={6} lg={4}>
                                        <FormSelect
                                            name="status"
                                            id="status"
                                            label={t`contract:contract_status`}
                                            options={CONTRACT_STATUS_OPTIONS(t)}
                                            required
                                        />
                                    </Col>
                                    <Col xs={12} md={6} lg={4}>
                                        <FormLabel>&nbsp</FormLabel>
                                        <FormCheck
                                            color="primary"
                                            name="isStoreContractDocument"
                                            id="isStoreContractDocument"
                                            label="Đã nhận bản lưu hợp đồng"
                                        />
                                    </Col>
                                </Row>
                            </section>
                        </CardBody>
                    </Card>
                    <br/>
                </Show>

                <Show when={props.contract}>
                    <br />
                    <Card>
                        <CardBody>
                            <section class="d-flex flex-column row-gap-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <header class="section-header">
                                        Phụ lục hợp đồng
                                    </header>
                                    <Show when={true}>
                                        <Tooltip content="Thêm phụ lục">
                                            <Button 
                                                color="second"
                                                href={`/contract/${params.code}/annex/new`}
                                                startIcon={<MdiPlusBox class="fs-4" />}>
                                            </Button>
                                        </Tooltip>
                                    </Show>
                                </div>
                                
                                <Row class="row-gap-3">
                                    <ContractAnnexTable 
                                        annexes={props.annexes} 
                                        total={0} 
                                        bidMap={null}
                                    />
                                </Row>
                            </section>
                        </CardBody>
                    </Card>
                    <br/>
                </Show>
                
                <Index each={props.hookForm.data("products")}>
                    {(product, index) => (
                        <>
                            <Card>
                                <CardBody>
                                    <section class="d-flex flex-column row-gap-3">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <header class="section-header">
                                                Sản phẩm thứ {index+1}
                                            </header>
                                            <Show when={index+1 == props.hookForm.data("products").length }>
                                                <Tooltip content="Thêm sản phẩm">
                                                    <Button 
                                                        color="second"
                                                        onClick={props.hookForm.addField(`products`, {}, props.hookForm.data('products').length)}
                                                        startIcon={<MdiPlusBox class="fs-4" />}>
                                                    </Button>
                                                </Tooltip>
                                            </Show>
                                            <Show when={index+1 != props.hookForm.data("products").length }>
                                                <Tooltip content="Xoá sản phẩm">
                                                    <Button 
                                                        color="second" 
                                                        onClick={() => props.hookForm.unsetField(`products.${index}`)}
                                                        startIcon={<MdiMinusBox class="fs-4" />}>
                                                    </Button>
                                                </Tooltip>
                                            </Show>
                                        </div>
                                        <Row class="row-gap-3">
                                            <Col xs={12} md={6} lg={3}>
                                                <ProductSelectAutocomplete
                                                    name={`products.${index}.productID`}
                                                    id={`products.${index}.productID`}
                                                    placeholder={t`common:placeholder.under_product`}
                                                    label={t`common:label.under_product`}
                                                    onChange={(e) => props.hookForm.setFields(`products.${index}.sku`, e.data.sku)}
                                                    invalid={!isEmptyObject(props.hookForm.errors("product.productID"))}
                                                    feedbackInvalid={props.hookForm.errors("product.productID")}
                                                />
                                            </Col>
                                            <Col xs={12} md={6} lg={3}>
                                                <FormInput
                                                    name={`products.${index}.price`}
                                                    id={`products.${index}.price`}
                                                    label="Giá"
                                                    type="number"
                                                    text={props.hookForm.data(`products.${index}.price`) ? formatCurrency(props.hookForm.data(`products.${index}.price`)) : ""}
                                                    required
                                                />
                                            </Col>
                                            <Col xs={12} md={6} lg={3}>
                                                <FormInput
                                                    name={`products.${index}.quantity`}
                                                    id={`products.${index}.quantity`}
                                                    label="SL trúng thầu"
                                                    type="number"
                                                    placeholder="Nhập số lượng"
                                                    text={props.hookForm.data(`products.${index}.quantity`) ? formatNumber(props.hookForm.data(`products.${index}.quantity`)) : ""}
                                                    
                                                />
                                            </Col>
                                            <Col xs={12} md={6} lg={3}>
                                                <FormSelect
                                                    name={`products.${index}.unit`}
                                                    id={`products.${index}.unit`}
                                                    label="Đơn vị tính"
                                                    options={UNIT_OPTIONS(t)}
                                                    invalid={!isEmptyObject(props.hookForm.errors(`products.${index}.unit`))}
                                                    feedbackInvalid={props.hookForm.errors(`products.${index}.unit`)}
                                                    required
                                                />
                                            </Col>
                                        </Row>
                                    </section>
                                </CardBody>
                            </Card>
                            <br/>
                        </>
                    )}
                </Index>
                <br />
                <Show when={true}>
                    <div class="submit-wrapper">
                        <Tooltip content={t`common:button.save`}>
                            <Button color="success" class="ms-2" type="submit">
                                <SaveIcon class="fs-5" />
                                {t`common:button.save`}
                            </Button>
                        </Tooltip>
                    </div>
                </Show>

            </form>
        </Row>
    );
}
