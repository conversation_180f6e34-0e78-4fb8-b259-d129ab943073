import {
	<PERSON><PERSON>,
	Col,
	Row,
	useToast,
	useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { Show } from "solid-js";
import { A, createRouteData, redirect, useNavigate, useParams, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { LotFormCreate } from "~/containers/LotFormCreate";
import { getBid } from "~/services/tender/bid.client";
import { getLot, updateLot } from "~/services/tender/lot.client";
import { validateLotForm } from "~/services/tender/lot.model";
import MdiKeyboardReturn from '~icons/mdi/keyboard-return'

export function routeData({ location, params }) {
	return createRouteData(
		async ([query]) => {
			const lotID = +query.lotID;
			const bidID = +params.code;

			if (!lotID) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			if (!bidID) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			const res = await getLot({
				lotID,
				option: {},
			});
			if (res.status === API_STATUS.NOT_FOUND) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}
			if (res.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

			const resBid = await getBid({
				bidID,
				option: {},
			});

			if (resBid.status === API_STATUS.NOT_FOUND) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}

			return {
				lot: res.data?.[0],
				bid: resBid.data[0],
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
    const getData = useRouteData();
    const params = useParams();

	return (
		<AppLayout
			pageTitle="bid:edit_bid"
			namespaces={["bid"]}
			breadcrumbs={[BREADCRUMB.BID,
            {
                label: "Chỉnh sửa thông tin gói thầu",
                link: `/bid/${params.code}/lot`,
            },
            {
                label: "Cập nhật sản phẩm",
                link: `/bid/${params.code}/lot`,
            }]}
		>
            <Show when={getData()}>
                <PageEditLot lotInfo={getData()?.lot} bidInfo={getData()?.bid}/>
            </Show>
		</AppLayout>
	);
};

function PageEditLot(props) {
    const params = useParams();
	const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();

    const hookForm = createForm({
		initialValues: {
			lotLineID: props.lotInfo.lotLineID,
			lotName: props.lotInfo.lotName,
			lotPrice: props.lotInfo.lotPrice,
			productID: props.lotInfo.productID,
			quantity: props.lotInfo.quantity,
			lotAmount: props.lotInfo.lotAmount,
			unit: props.lotInfo.unit,
			registrationNo: props.lotInfo.registrationNo,
			guaranteeAmount: props.lotInfo.guaranteeAmount,
			status: props.lotInfo.status,
			groupMedicine: props.lotInfo.groupMedicine,
			volume: props.lotInfo.volume,
			note: props.lotInfo.note,
			manufacturerName: props.lotInfo.manufacturerName,
			originName: props.lotInfo.originName,
			vat: props.lotInfo.vat,
		},
		validate: (values) => validateLotForm(values, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
			const lotID = props.lotInfo.lotID
			const groupMedicine = +(data.groupMedicine)
			const productID = +(data.productID)
			data.vat = +(data.vat)
            
			const res = await updateLot({...data, lotID, groupMedicine, productID });
			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Update lot:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:update_success`);
                window.location.reload();
            }
            return;
		},
	});
    return (
        <Show when={true}>
			<Row class="gap-3">
				<Col xs={12}>
					<div class="d-flex align-items-center justify-content-between">
						<A href={`/bid/${params.code}`}>
							<Button color="secondary" startIcon={<MdiKeyboardReturn />} >
								Quay lại
							</Button>
						</A>
						<h1 class="page-title">Cập nhật sản phẩm</h1>
					</div>
				</Col>
			</Row>
			<br/>
            <LotFormCreate isEdit bid={props.bidInfo}  hookForm={hookForm} lot={props.lotInfo}/>
        </Show>
    );
}