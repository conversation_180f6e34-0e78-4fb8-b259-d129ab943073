import { <PERSON><PERSON>, Card, CardBody, Col, FormInput, FormSelect, Row, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatCurrency, isEmptyObject } from "@buymed/solidjs-component/utils";
import { Show } from "solid-js";
import { BID_CATEGORY, BID_STATUS } from "~/services/tender/bid.model";
import { GROUP_MEDICINE_OPTIONS, LOT_STATUS, LOT_STATUS_OPTIONS, UNIT_OPTIONS } from "~/services/tender/lot.model";
import SaveIcon from "~icons/mdi/content-save";
import { ProductSelectAutocomplete } from "../ProductSelectAutocomplete";
import { UnitSelectAutocomplete } from "../UnitSelectAutocomplete";

export function LotFormCreate(props) {
    const { t } = useTranslate();

    function updateLotAmount() {
		const price = props.hookForm.data('lotPrice') || 0;
		const qty = props.hookForm.data('quantity') || 0;
		props.hookForm.setFields('lotAmount', price * qty);
	}

	return (
		<Row class="gap-3">
			<form ref={props.hookForm.form}>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin sản phẩm</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <Show when={props.isEdit}>
                                        <ProductSelectAutocomplete
                                            name={`productID`}
                                            id={`productID`}
                                            productID={props.lot.productID}
                                            placeholder={t`common:placeholder.under_product`}
                                            label={t`common:label.under_product`}
                                            onChange={(e) => {
                                                props.hookForm.setFields(`sku`, e.data.sku);
                                                props.hookForm.setFields(`productCode`, e.data.code);
                                            }}
                                            disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                            invalid={!isEmptyObject(props.hookForm.errors("product.productID"))}
                                            feedbackInvalid={props.hookForm.errors("product.productID")}
                                        />
                                    </Show>
                                    <Show when={!props.isEdit}>
                                        <ProductSelectAutocomplete
                                            name={`productID`}
                                            id={`productID`}
                                            placeholder={t`common:placeholder.under_product`}
                                            label={t`common:label.under_product`}
                                            onChange={(e) => {
                                                console.log(`change ${props.hookForm.data("productID")}`)
                                                props.hookForm.setFields(`sku`, e.data.sku);
                                                props.hookForm.setFields(`lotName`, e.data.name);
                                                props.hookForm.setFields(`unit`, e.data.unit);
                                                props.hookForm.setFields(`productCode`, e.data.code);
                                            }}
                                            invalid={!isEmptyObject(props.hookForm.errors("product.productID"))}
                                            feedbackInvalid={props.hookForm.errors("product.productID")}
                                        />
                                    </Show>
                                    
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="lotName"
                                        id="lotName"
                                        label="Tên lô sản phẩm"
                                        invalid={!isEmptyObject(props.hookForm.errors("lotName"))}
										feedbackInvalid={props.hookForm.errors("lotName")}
                                        disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                    />
                                </Col>
                                <Show when={props.bid && props.bid.status !== BID_STATUS.WAITING} fallback={
                                    <Col xs={12} md={6} lg={4}>
                                        <Show when={props.hookForm.data(`productID`)} fallback={
                                            <FormSelect
                                                name="unit"
                                                id="unit"
                                                label="Đơn vị tính"
                                                placeholder="Chọn"
                                                options={UNIT_OPTIONS(t)}
                                                invalid={!isEmptyObject(props.hookForm.errors("unit"))}
                                                feedbackInvalid={props.hookForm.errors("unit")}
                                                required
                                            />
                                        }>
                                            <UnitSelectAutocomplete
                                                name="unit"
                                                id="unit"
                                                label="Đơn vị tính"
                                                productID={props.hookForm.data(`productID`)}
                                                invalid={!isEmptyObject(props.hookForm.errors("unit"))}
                                                feedbackInvalid={props.hookForm.errors("unit")}
                                                required
                                                disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                            />
                                        </Show>
                                    </Col>
                                }>
                                    <Col xs={12} md={6} lg={4}>
                                        <FormInput
                                            name="unit"
                                            id="unit"
                                            label="Đơn vị tính"
                                            disabled
                                        /> 
                                    </Col> 
                                </Show>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="lotLineID"
                                        id="lotLineID"
                                        label="STT Lô"
                                        type="number"
                                        invalid={!isEmptyObject(props.hookForm.errors("lotLineID"))}
										feedbackInvalid={props.hookForm.errors("lotLineID")}
                                        // disabled={!props.bid || props.bid.status !== BID_STATUS.WAITING}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="registrationNo"
                                        id="registrationNo"
                                        label="SĐK/GPNK"
                                        invalid={!isEmptyObject(props.hookForm.errors("registrationNo"))}
										feedbackInvalid={props.hookForm.errors("registrationNo")}
                                        disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="volume"
                                        id="volume"
                                        label="Quy cách đóng gói"
                                        text="Ví dụ: Hộp 30 vỉ x 10 viên"
                                        disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                    />
                                </Col>
                                <Show when={props.bid?.mainCategory !== BID_CATEGORY.MEDICAL_SUPPLIES}>
                                    <Col xs={12} md={6} lg={4}>
                                        <FormSelect
                                            name="groupMedicine"
                                            id="groupMedicine"
                                            label="Nhóm"
                                            options={GROUP_MEDICINE_OPTIONS(t, props.bid?.mainCategory)}
                                            invalid={!isEmptyObject(props.hookForm.errors("groupMedicine"))}
                                            feedbackInvalid={props.hookForm.errors("groupMedicine")}
                                            disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                        />
                                    </Col>
                                </Show>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="manufacturerName"
                                        id="manufacturerName"
                                        label="Nhà sản xuất"
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="originName"
                                        id="originName"
                                        label="Nước sản xuất"
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin dự thầu</header>
                            <Row class="row-gap-3">
							<Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="lotPrice"
                                        id="lotPrice"
										type="number"
                                        label="Giá dự thầu"
                                        text={formatCurrency(props.hookForm.data(`lotPrice`))}
										onChange={updateLotAmount}
                                        placeholder="Nhập giá dự thầu"
                                        invalid={!isEmptyObject(props.hookForm.errors("lotPrice"))}
										feedbackInvalid={props.hookForm.errors("lotPrice")}
                                        disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                        required
                                    />
                                </Col>
								<Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="quantity"
                                        id="quantity"
										type="number"
                                        label="SL"
										onChange={updateLotAmount}
                                        placeholder="Nhập số lượng"
                                        invalid={!isEmptyObject(props.hookForm.errors("quantity"))}
										feedbackInvalid={props.hookForm.errors("quantity")}
                                        disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                        required
                                    />
                                </Col>
								<Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="lotAmount"
                                        id="lotAmount"
										type="number"
                                        label="Thành tiền"
                                        placeholder=""
                                        text={formatCurrency(props.hookForm.data(`lotAmount`))}
                                        disabled
                                    />
                                </Col>
							</Row>
							<Row class="row-gap-3">
								<Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="guaranteeAmount"
                                        id="guaranteeAmount"
										type="number"
                                        label="Giá trị đảm bảo dự thầu"
                                        text={formatCurrency(props.hookForm.data(`guaranteeAmount`))}
                                        invalid={!isEmptyObject(props.hookForm.errors("guaranteeAmount"))}
										feedbackInvalid={props.hookForm.errors("guaranteeAmount")}
                                        disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="vat"
                                        id="vat"
                                        options={[
                                            {
                                                value: 5,
                                                label: "5%",
                                            },
                                            {
                                                value: 8,
                                                label: "8%",
                                            },
                                            {
                                                value: 10,
                                                label: "10%",
                                            },
                                        ]}
                                        label="VAT"
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <Show when={props.lot}>
                    <br/>
                    <Card>
                        <CardBody>
                            <section class="d-flex flex-column row-gap-3">
                                <header class="section-header">Kết quả dự thầu</header>
                                <Row class="row-gap-3">
                                    <Col xs={12} md={6} lg={4}>
                                        <FormSelect
                                            name="status"
                                            id="status"
                                            label={t`bid:lot_status`}
                                            options={LOT_STATUS_OPTIONS(t)}
                                            disabled={props.bid && props.bid.status !== BID_STATUS.WAITING}
                                            required
                                        />
                                    </Col>
                                    <Col xs={12} md={6} lg={4}>
                                        <FormInput
                                            name="note"
                                            id="note"
                                            label="Ghi chú"
                                        />
                                    </Col>
                                </Row>
                            </section>
                        </CardBody>
                    </Card>
                </Show>
                
                <br/>
                <Show when={true}>
                    <div class="submit-wrapper">
                        <Tooltip content={t`common:button.save`}>
                            <Button color="success" class="ms-2" type="submit">
                                <SaveIcon class="fs-5" />
                                {t`common:button.save`}
                            </Button>
                        </Tooltip>
                    </div>
                </Show>
            </form>
		</Row>
	);
}