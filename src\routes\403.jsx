import { A, useLocation } from "@solidjs/router";
import { NotFound, useTranslate } from "@buymed/solidjs-component/components";
import { HttpStatusCode } from "solid-start/server";
import AppLayout from "~/components/Layout/AppLayout";

export default () => {
	return (
		<AppLayout pageTitle="403:pageTitle" namespaces={["403"]}>
			<HttpStatusCode code={403} />
			<Page403 />
		</AppLayout>
	);
};

function Page403() {
	const { t } = useTranslate();
	const location = useLocation();

	return (
		<NotFound
			title={t`403:title`}
			subTitle={t`403:subTitle`}
			actionContent={
				<>
					{t`403:actionContent`}{" "}
					<A href={location.query.pathAuth} class="text-success">
						{t`403:returnPageTitle`}
					</A>
				</>
			}
		/>
	);
}
