import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary, Show } from "solid-js";
import { createRouteData, redirect, useNavigate, useRouteData } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { BenaficiaryForm } from "~/containers/Benaficiary/form";
import { HeaderDetail } from "~/containers/Benaficiary/header-detail";
import { FormProvider } from "~/contexts/FormContext";
import { getBeneficiaryList, putBeneficiary } from "~/services/tender/beneficiary.client";
import { BENEFICIARY_STATUS, validateBeneficiaryForm } from "~/services/tender/beneficiary.model";
import { ERROR_MESSAGE } from "~/utils/error-code";

export function routeData({ params }) {
	return createRouteData(
		async () => {
			const code = params.code;

			if (!code) {
				// TODO Better: Redirect to listing + flash
				throw redirect("/404");
			}
			const _res = await getBeneficiaryList({
				q: { code },
				offset: 0,
				limit: 1,
			});
			if (_res.status !== API_STATUS.OK) {
				throw redirect("/404");
			}

			return {
				beneficiary: _res.data[0],
			};
		},
		{
			key: () => {
				return [params.code];
			},
		}
	);
}

export default () => {
	const getData = useRouteData();
	return (
		<AppLayout
			pageTitle="beneficiary:edit"
			namespaces={["beneficiary"]}
			breadcrumbs={[BREADCRUMB.BENEFICIARY, BREADCRUMB.EDIT_BENEFICIARY]}
		>
			<Show when={getData()} fallback={<ErrorMessage />}>
				<PageContainer beneficiary={getData()?.beneficiary} />
			</Show>
		</AppLayout>
	);
};

function PageContainer(props) {
	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Thông tin khách hàng</h1>
				</Col>
				<HeaderDetail
					tabs={[
						{
							name: "Thông tin",
							href: "info",
						},
						{
							name: "Hợp đồng",
							href: "contract",
						},
						{
							name: "Đơn hàng",
							href: "order",
						},
					]}
				/>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddContract beneficiary={props.beneficiary} />
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddContract(props) {
	const beneficiary = props.beneficiary;
	const { t } = useTranslate();
	const toast = useToast();
	const navigate = useNavigate();

	const initialValues = {
		name: beneficiary?.name ?? "",
		address: beneficiary?.address ?? "",
		phoneNumber: beneficiary?.phoneNumber ?? "",
		taxCode: beneficiary?.taxCode ?? "",
		status: beneficiary?.status === BENEFICIARY_STATUS.ACTIVE ? true : false,
		email: beneficiary?.email ?? "",
	};
	const hookForm = createForm({
		initialValues,
		validate: (values) => {
			return validateBeneficiaryForm(values);
		},

		onSubmit: async (values) => {
			console.log(values.status, "values.status");
			try {
				const res = await putBeneficiary({
					...values,
					code: beneficiary.code,
					status: values.status ? BENEFICIARY_STATUS.ACTIVE : BENEFICIARY_STATUS.INACTIVE,
				});
				if (res.status !== API_STATUS.OK) {
					const errorCode = res?.errorCode;
					const message = ERROR_MESSAGE?.[errorCode] || "";
					return toast.error(
						message
							? message
							: t("common:notify.action_fail", { error: "Vui lòng kiểm tra lại" })
					);
				}
				toast.success("Cập nhật thành công");
				navigate(`/beneficiary/${beneficiary.code}`);
			} catch (error) {
				console.error("[Error] Create beneficiary:", error);
				toast.error(t("common:notify.action_fail", { error: error.message }));
			}
		},
	});

	return (
		<FormProvider form={hookForm}>
			<BenaficiaryForm />
		</FormProvider>
	);
}
