import { <PERSON><PERSON>, Card, CardBody, Col, DatePicker, EHTMLType, FileInput, FormCheck, FormInput, FormLabel, FormSelect, Row, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatCurrency, isEmptyObject } from "@buymed/solidjs-component/utils";
import { Index, Show } from "solid-js";
import { CONTRACT_STATUS_OPTIONS } from "~/services/tender/contract.model";
import SaveIcon from "~icons/mdi/content-save";
import MdiMinusBox from '~icons/mdi/minus-box';
import MdiPlusBox from '~icons/mdi/plus-box';
import { LotSelectAutoComplete } from "../LotSelectAutoComplete";
import { ProductSelectAutocomplete } from "../ProductSelectAutocomplete";
import { useAuth } from "~/contexts/AuthContext";

export function ContractAnnexFormCreate(props) {
    const { t } = useTranslate();
    const { documentToken } = useAuth();
   
    function updateShowAppendProduct() {
		let isAppendProduct = props.hookForm.data('isAppendProduct');
        props.hookForm.setFields('isAppendProduct', !isAppendProduct);
        if(!isAppendProduct) {
            props.hookForm.setFields(`products`, [{}])
        } else {
            props.hookForm.unsetField(`products`)
        }
	}

    return (
        <Row class="gap-3">
            <form ref={props.hookForm.form}>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="contractCode"
                                        id="contractCode"
                                        label={t`contract:name`}
                                        options={props.contractOptions || []}
                                        invalid={!isEmptyObject(props.hookForm.errors("contractCode"))}
                                        feedbackInvalid={props.hookForm.errors("contractCode")}
                                        disabled
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="annexNumber"
                                        id="annexNumber"
                                        label="Mã phụ lục"
                                        disabled={props.isEdit === true}
                                        placeholder="Nhập mã phụ lục hợp đồng"
                                        invalid={!isEmptyObject(props.hookForm.errors("annexNumber"))}
                                        feedbackInvalid={props.hookForm.errors("annexNumber")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="name"
                                        id="name"
                                        label="Tên phụ lục hợp đồng"
                                        placeholder="Nhập tên phụ lục hợp đồng"
                                        invalid={!isEmptyObject(props.hookForm.errors("name"))}
                                        feedbackInvalid={props.hookForm.errors("name")}
                                        required
                                    />
                                </Col>
                                <Col xs={12}>
                                    <FormInput
                                        name="content"
                                        id="content"
                                        label="Nội dung"
                                        placeholder="Nhập nội dung phụ lục"
                                        invalid={!isEmptyObject(props.hookForm.errors("content"))}
                                        feedbackInvalid={props.hookForm.errors("content")}
                                        required
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
                                        label="Thời gian ký"
                                        name="contractDate"
                                        id="contractDate"
                                        format="dd/MM/yyyy"
                                        placeholder="Chọn"
                                        invalid={!isEmptyObject(props.hookForm.errors("contractDate"))}
                                        feedbackInvalid={props.hookForm.errors("contractDate")}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <DatePicker
                                        label="Thời gian hết hạn"
                                        name="expireDate"
                                        id="expireDate"
                                        format="dd/MM/yyyy"
                                        placeholder="Chọn"
                                        invalid={!isEmptyObject(props.hookForm.errors("expireDate"))}
                                        feedbackInvalid={props.hookForm.errors("expireDate")}
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Tài liệu đính kèm</header>
                            <Row class="row-gap-3">
                                <Col xs={12} >
                                    <Index each={props.hookForm.data("attachments")}>
                                        {(attachment, index) => (
                                            <FileInput
                                                documentToken={documentToken()}
                                                mode={EHTMLType.Documents}
                                                type={EHTMLType.Documents}
                                                extensions=".xlsx, .xls, .doc, .docx, .ppt, .pptx, .pdf"
                                                onAdd={(newFile) => {
                                                    console.log("newFile=", newFile)
                                                    props.hookForm.setData("attachments", [
                                                        newFile.previewLink,
                                                        ...props.hookForm.data("attachments"),
                                                    ]);
                                                }}
                                                onRemove={() =>
                                                    props.hookForm.setData(
                                                        "attachments",
                                                        props.hookForm.data("attachments")?.filter(
                                                            (_, i) => i !== index
                                                        )
                                                    )
                                                }
                                                value={attachment}
                                            />
                                        )}
                                    </Index>
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Show when={props.hookForm.data('bidID')}>
                    <Show when={true}>
                        <Col xs={12} md={6} lg={4}>
                            <FormLabel>&nbsp</FormLabel>
                            <FormCheck
                                color="primary"
                                name="isAppendProduct"
                                id="isAppendProduct"
                                label="Phụ lục hợp đồng có chỉnh sửa sản phẩm"
                                onClick={() => updateShowAppendProduct()}
                            />
                        </Col>
                    </Show>
                    <Index each={props.hookForm.data("products")}>
                        {(product, index) => (
                            <>
                                <Card>
                                    <CardBody>
                                        <section class="d-flex flex-column row-gap-3">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <header class="section-header">
                                                    Sản phẩm thứ {index+1}
                                                </header>
                                                <Show when={index+1 == props.hookForm.data("products").length }>
                                                    <Tooltip content="Thêm sản phẩm">
                                                        <Button 
                                                            color="second"
                                                            onClick={props.hookForm.addField(`products`, {}, props.hookForm.data('products').length)}
                                                            startIcon={<MdiPlusBox class="fs-4" />}>
                                                        </Button>
                                                    </Tooltip>
                                                </Show>
                                                <Show when={index+1 != props.hookForm.data("products").length }>
                                                    <Tooltip content="Xoá sản phẩm">
                                                        <Button 
                                                            color="second" 
                                                            onClick={() => props.hookForm.unsetField(`products.${index}`)}
                                                            startIcon={<MdiMinusBox class="fs-4" />}>
                                                        </Button>
                                                    </Tooltip>
                                                </Show>
                                            </div>
                                            <Row class="row-gap-3">
                                                <Col xs={12} md={6} lg={3}>
                                                    <LotSelectAutoComplete
                                                        name={`products.${index}.lotID`}
                                                        id={`products.${index}.lotID`}
                                                        placeholder="Chọn lô sản phẩm"
                                                        label="Lô sản phẩm"
                                                        bidID={props.hookForm.data('bidID')}
                                                        onChange={(e) => {
                                                            props.hookForm.setFields(`products.${index}.price`, e.data.lotPrice)
                                                            props.hookForm.setFields(`products.${index}.productID`, e.productID)
                                                            props.hookForm.setFields(`products.${index}.productCode`, e.data.productCode)
                                                            props.hookForm.setFields(`products.${index}.lotName`, e.name)
                                                            props.hookForm.setFields(`products.${index}.unit`, e.data.unit)
                                                        }}
                                                        invalid={!isEmptyObject(props.hookForm.errors(`product${index}.lotID`))}
                                                        feedbackInvalid={props.hookForm.errors(`products.${index}.lotID`)}
                                                    />
                                                </Col>
                                                <Col xs={12} md={6} lg={3}>
                                                    <FormInput
                                                        name={`products.${index}.price`}
                                                        id={`products.${index}.price`}
                                                        label="Giá trúng thầu"
                                                        type="number"
                                                        text={
                                                            props.hookForm.data(`products.${index}.price`) ? 
                                                                formatCurrency(props.hookForm.data(`products.${index}.price`)) + "/" + props.hookForm.data(`products.${index}.unit`) : 
                                                                ""
                                                            }
                                                        // disabled
                                                    />
                                                </Col>
                                                <Col xs={12} md={6} lg={3}>
                                                    <FormInput
                                                        name={`products.${index}.quantity`}
                                                        id={`products.${index}.quantity`}
                                                        label="Số lượng"
                                                        type="number"
                                                        placeholder="Nhập số lượng"
                                                        required
                                                    />
                                                </Col>
                                                <Col xs={12} md={6} lg={3}>
                                                    <FormInput
                                                        name={`products.${index}.productReplaceCode`}
                                                        id={`products.${index}.productReplaceCode`}
                                                        label="Mã line cần hiệu chỉnh"
                                                        type="text"
                                                        placeholder=""
                                                    />
                                                </Col>
                                            </Row>
                                        </section>
                                    </CardBody>
                                </Card>
                                <br/>
                            </>
                        )}
                    </Index>
                </Show>
                <br />
                <Show when={true}>
                    <div class="submit-wrapper">
                        <Tooltip content={t`common:button.save`}>
                            <Button color="success" class="ms-2" type="submit">
                                <SaveIcon class="fs-5" />
                                {t`common:button.save`}
                            </Button>
                        </Tooltip>
                    </div>
                </Show>

            </form>
        </Row>
    );
}