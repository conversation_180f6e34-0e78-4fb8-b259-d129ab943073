import { createForm } from "@felte/solid";
import { useNavigate } from "@solidjs/router";
import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	DateRangePicker,
	FormInput,
	Row,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { isEmptyObject, sanitize } from "@buymed/solidjs-component/utils";
import { useSearchParams } from "solid-start/router";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";

export function LotFormFilter() {
	const { t } = useTranslate();
	const navigate = useNavigate();

	const [searchParams, setSearchParams] = useSearchParams();

	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			setSearchParams({
				q,
				page: undefined,
				limit: undefined,
			});
		},
	});

	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	return (
		<Card>
			<CardBody>
				<form ref={form}>
					<Row class="row-gap-3">
						<Col xs={12} md={6} lg={4}>
							<FormInput
								name="productName"
								type="search"
								label={t`bid:product.name`}
								placeholder={t`bid:product.enter_name`}
							/>
						</Col>
                        <Col xs={12} md={6} lg={4}>
							<FormInput
								name="contractNo"
								type="search"
								label={t`bid:contract.contract_number`}
								placeholder={t`bid:contract.enter_contract_number`}
							/>
						</Col>
						<Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
							<Button
								color="secondary"
								startIcon={<FilterRemoveIcon />}
								onClick={onClearFilter}
							>
								{t`common:button.clearFilter`}
							</Button>

							<Button type="submit" color="success" startIcon={<MagnifyIcon />}>
								{t`common:button.applyButton`}
							</Button>
						</Col>
					</Row>
				</form>
			</CardBody>
		</Card>
	);
}
