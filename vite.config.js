import solid from "solid-start/vite";
import Icons from "unplugin-icons/vite";
import { defineConfig, loadEnv } from "vite";

export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd());

	return {
		plugins: [
			solid({
				ssr: false,
			}),
			Icons({
				compiler: "solid",
			}),
		],
		server: {
			proxy: {
				"/backend": {
					target: env.VITE_API_HOST,
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/backend/, ""),
					secure: false,
					ws: true,
				},
				"/backend-next": {
					target: env.VITE_API_NEXT_HOST,
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/backend-next/, ""),
					secure: false,
					ws: true,
				},
			},
		},
	};
});
