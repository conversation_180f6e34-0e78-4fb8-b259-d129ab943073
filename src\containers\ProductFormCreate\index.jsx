import { <PERSON><PERSON>, Card, CardBody, Col, FormInput, FormSelect, Row, Tooltip, useTranslate } from "@buymed/solidjs-component/components";
import { formatCurrency, formatNumber, isEmptyObject } from "@buymed/solidjs-component/utils";
import { Show } from "solid-js";
import SaveIcon from "~icons/mdi/content-save";
import { LotSelectAutoComplete } from "../LotSelectAutoComplete";
import { ProductSelectAutocomplete } from "../ProductSelectAutocomplete";
import { ContractSelectAutocomplete } from "../ContractSelectAutocomplete";
import { CONTRACT_STATUS } from "~/services/tender/contract.model";
import { GROUP_MEDICINE_OPTIONS } from "~/services/tender/lot.model";

export function ProductFormCreate(props) {
    const { t } = useTranslate();

	return (
		<Row class="gap-3">
			<form ref={props.hookForm.form}>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <div class="d-flex align-items-center justify-content-between">
                                <header class="section-header">
                                    {t`product:product`}
                                </header>
                            </div>
                            <Row class="row-gap-3">
                                <Show when={props.isEdit}>
                                    <Col xs={12} md={6} lg={6}>
                                        <ContractSelectAutocomplete
                                            name={`contractID`}
                                            id={`contractID`}
                                            placeholder="Chọn hợp đồng"
                                            label="Hợp đồng"
                                            invalid={!isEmptyObject(props.hookForm.errors("contractID"))}
                                            feedbackInvalid={props.hookForm.errors("contractID")}
                                            onChange={(e) => props.hookForm.setFields('bidID', e.data.bidID)}
                                            disabled={props.isEdit}
                                        />
                                    </Col>
                                </Show>
                                <Show when={!props.isEdit}>
                                    <Col xs={12} md={6} lg={6}>
                                        <ContractSelectAutocomplete
                                            name={`contractID`}
                                            id={`contractID`}
                                            placeholder="Chọn hợp đồng"
                                            status={CONTRACT_STATUS.DRAFT}
                                            label="Hợp đồng"
                                            invalid={!isEmptyObject(props.hookForm.errors("contractID"))}
                                            feedbackInvalid={props.hookForm.errors("contractID")}
                                            onChange={(e) => {
                                                props.hookForm.setFields('bidID', e.data.bidID);
                                                props.hookForm.setFields('mainCategory', e.data.mainCategory)
                                            }}
                                            disabled={props.isEdit}
                                        />
                                    </Col>
                                </Show>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <LotSelectAutoComplete
                                        name={`lotID`}
                                        id={`lotID`}
                                        placeholder="Chọn lô sản phẩm"
                                        label="Lô sản phẩm"
                                        bidID={props.hookForm.data('bidID')}
                                        onChange={(e) => {
                                            props.hookForm.setFields(`price`, e.data.lotPrice)
                                            props.hookForm.setFields(`productID`, e.productID)
                                            props.hookForm.setFields(`productCode`, e.data.productCode)
                                            props.hookForm.setFields(`lotName`, e.name)
                                            props.hookForm.setFields(`unit`, e.data.unit)
                                        }}
                                        invalid={!isEmptyObject(props.hookForm.errors(`lotID`))}
                                        feedbackInvalid={props.hookForm.errors(`lotID`)}
                                        disabled={props.contract && props.contract.status !== CONTRACT_STATUS.DRAFT}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name={`price`}
                                        id={`price`}
                                        label={t`product:form.winning_price`}
                                        type="number"
                                        text={props.hookForm.data("price") ? formatCurrency(props.hookForm.data("price")) : ""}
                                        disabled
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name={`quantity`}
                                        id={`quantity`}
                                        label={t`product:form.winning_quantity`}
                                        type="number"
                                        placeholder={t`product:form.enter_quantity`}
                                        text={props.hookForm.data("quantity") ? formatNumber(props.hookForm.data("quantity")) : ""}
                                        required
                                        disabled={props.contract && props.contract.status !== CONTRACT_STATUS.DRAFT}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name={`unit`}
                                        id={`unit`}
                                        label="Đơn vị"
                                        disabled
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name={`registrationNo`}
                                        id={`registrationNo`}
                                        label="SDK/GPĐK"
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <FormSelect
                                        name="groupMedicine"
                                        id="groupMedicine"
                                        label="Nhóm"
                                        placeholder="Chọn"
                                        options={GROUP_MEDICINE_OPTIONS(t, props.hookForm.data("mainCategory"))}
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Show when={true}>
                    <div class="submit-wrapper">
                        <Tooltip content={t`common:button.save`}>
                            <Button color="success" class="ms-2" type="submit">
                                <SaveIcon class="fs-5" />
                                {t`common:button.save`}
                            </Button>
                        </Tooltip>
                    </div>
                </Show>
            </form>
		</Row>
	);
}