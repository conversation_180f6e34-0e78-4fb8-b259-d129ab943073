import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { callAPI } from "../callAPI";
import { Lot } from "./lot.model";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { QueryOption } from "./bid.model";

const URL = "/tender/core-tender/v1";

/** Create lot */
export async function createLot(data: Lot): Promise<APIResponse<Lot>> {
	return callAPI(HTTP_METHOD.POST, `${URL}/lot`, data);
}

/** Update lot information */
export async function updateLot(data: Partial<Lot>): Promise<APIResponse<Lot>> {
	return callAPI(HTTP_METHOD.PUT, `${URL}/lot`, data);
}

/** Get lot info */
export async function getLot({
	lotID,
	option,
}: {
	lotID?: number;
	option?: QueryOption;
}): Promise<APIResponse<Lot>> {
	return callAPI(HTTP_METHOD.GET, `${URL}/lot`, {
		lotID,
		option: option ? Object.keys(option).join(",") : undefined,
	});
}

/** Return a list of lot  */
export async function getLotList(
	input?: QueryInput<LotQuery, QueryOption>
): Promise<APIResponse<Lot>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/lot/list`, {
		...input,
		search: input.search ? String(input.search) : undefined,
	});
}

// ===================================================
// For QUERY
// ===================================================
export type LotQuery = {};

export type LotQueryOption = MasterDataOption & {};