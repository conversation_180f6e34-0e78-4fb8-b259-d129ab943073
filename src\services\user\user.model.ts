import { MasterDataOption } from "@buymed/solidjs-component/services";

export const USER_STATUS = {
	ACTIVE: "ACTIVE",
	INACTIVE: "INACTIVE",
} as const;

export const USER_STATUS_LABEL = {
	[USER_STATUS.ACTIVE]: "user:status.ACTIVE",
	[USER_STATUS.INACTIVE]: "user:status.INACTIVE",
} as const;

export const USER_STATUS_OPTIONS = (t) => [
	{ value: USER_STATUS.ACTIVE, label: t(USER_STATUS_LABEL[USER_STATUS.ACTIVE]) },
	{ value: USER_STATUS.INACTIVE, label: t(USER_STATUS_LABEL[USER_STATUS.INACTIVE]) },
];

export const GENDER = {
	MALE: "MALE",
	FEMALE: "FEMALE",
	OTHER: "OTHER",
} as const;

export const GENDER_LABEL = {
	[GENDER.MALE]: "user:male_gender",
	[GENDER.FEMALE]: "user:female_gender",
	[GENDER.OTHER]: "user:other_gender",
} as const;

export interface User {
	accountID?: number;
	email: string;
	username: string;
	fullname: string;
	gender?: keyof typeof GENDER;
	status?: keyof typeof USER_STATUS;
	createdTime?: string;
	lastUpdatedTime?: string;

	tempPassword?: string;
}

// ===================================================
// For QUERY
// ===================================================
export type UserQuery = {
	accountIDs?: number[];
	accountID?: number;
	email?: string;
	username?: string;
	fullname?: string;
};

export type QueryOption = MasterDataOption & {
	account?: boolean;
};

export type ResetUserAccountPasswordInput = {
	accountID: number;
};

// ===================================================
// Validation
// ===================================================
const ACCEPTED_CHARS = "0123456789qwertyuiopasdfghjklzxcvbnm@._-";
const USERNAME_MIN_LENGTH = 4;
const USERNAME_MAX_LENGTH = 30;
const MAXLENGTH = 50;
export function validateUserForm(values, t) {
	const err: any = {};

	// Email
	if (!values.email) {
		err.email = t`user:company_email_required`;
	} else if (!/^\S+@\S+\.\S+$/.test(values.email?.trim())) {
		err.email = t`user:email_validate`;
	}

	// Username
	if (!values.username) {
		err.username = t`user:username_is_not_empty`;
	} else if (values.username?.length < USERNAME_MIN_LENGTH) {
		err.username = t("user:username_min_length", { min: USERNAME_MIN_LENGTH });
	} else if (values.username?.length > USERNAME_MAX_LENGTH) {
		err.username = t("user:username_max_length", { max: USERNAME_MAX_LENGTH });
	} else if (values.username?.indexOf(" ") >= 0) {
		err.username = t("user:username_max_length", { max: USERNAME_MAX_LENGTH });
	} else {
		for (let i = 0; i < values.username.length; i++) {
			if (!ACCEPTED_CHARS.includes(values.username[i])) {
				err.username = t`user:errors.USERNAME_INVALID_CHAR`;
			}
		}
	}

	// Fullname
	if (!values.fullname) {
		err.fullname = t`user:fullname_is_not_empty`;
	} else if (values.fullname.length > MAXLENGTH) {
		err.fullname = t("user:name_max_length", { max: MAXLENGTH });
	}

	return err;
}
