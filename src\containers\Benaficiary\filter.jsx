import { createForm } from "@felte/solid";
import { useNavigate } from "@solidjs/router";
import {
	But<PERSON>,
	Card,
	CardBody,
	Col,
	DEFAULT_LIMIT,
	Row,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS, isEmptyObject, sanitize } from "@buymed/solidjs-component/utils";
import { useSearchParams } from "solid-start/router";
import FilterRemoveIcon from "~icons/mdi/filter-remove";
import MagnifyIcon from "~icons/mdi/magnify";
import { AutoCompleteTender } from "~/components/Autocomplete";
import { getBeneficiaryList } from "~/services/tender/beneficiary.client";

export function BenaficiaryFilter() {
	const { t } = useTranslate();
	const navigate = useNavigate();
	const [searchParams, setSearchParams] = useSearchParams();

	const { form, unsetField, data } = createForm({
		initialValues: JSON.parse(searchParams.q || "{}"),
		onSubmit: (values) => {
			let q = sanitize(values, { removeEmptyString: true, trim: true });
			q = isEmptyObject(q) ? undefined : JSON.stringify(q);

			setSearchParams({
				q,
				page: undefined,
				limit: undefined,
			});
		},
	});
	function onClearFilter() {
		// Can't use `reset`, because calling `reset()` will reset to `initialValues`.
		// https://felte.dev/docs/solid/helper-functions#reset
		Object.keys(data()).forEach(unsetField);

		navigate(window.location.pathname);
	}

	const handleFetchData = async ({ search }) => {
		const res = await getBeneficiaryList({
			search,
			limit: DEFAULT_LIMIT,
			offset: 0,
		});

		if (res.status !== API_STATUS.OK) {
			console.error("[Error] fetch beneficiaryOptions", res);
			return [];
		}

		return res.data;
	};
	return (
		<Card>
			<CardBody>
				<form ref={form}>
					<Row class="row-gap-3">
						<Col xs={12} md={6} lg={4}>
							<AutoCompleteTender
								name={"code"}
								placeholder={"Tìm kiếm khách hàng"}
								label={"Tìm kiếm"}
								handleFetchData={handleFetchData}
								fieldKey="code"
								fieldValue="name"
								renderOption={(props, { data }) => (
									<li {...props}>
										<b>
											{data.beneficiaryID}&nbsp;-&nbsp;{data.name}
										</b>
									</li>
								)}
							/>
						</Col>
						<Col xs={12} class="d-flex justify-content-end gap-3 ms-auto">
							<Button
								color="secondary"
								startIcon={<FilterRemoveIcon />}
								onClick={onClearFilter}
							>
								{t`common:button.clearFilter`}
							</Button>

							<Button type="submit" color="success" startIcon={<MagnifyIcon />}>
								{t`common:button.applyButton`}
							</Button>
						</Col>
					</Row>
				</form>
			</CardBody>
		</Card>
	);
}
