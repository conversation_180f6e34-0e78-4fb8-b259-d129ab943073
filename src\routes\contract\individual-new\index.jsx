import {
    Col,
    ErrorMessage,
    Row,
    useToast,
    useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary } from "solid-js";
import { useNavigate } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { ContractFormCreate } from "~/containers/ContractFormCreate";
import { ContractIndividualFormCreate } from "~/containers/ContractIndividualFormCreate";
import { createContract } from "~/services/tender/contract.client";
import { CONTRACT_TYPE, validateContractForm } from "~/services/tender/contract.model";

export default () => {
	return (
		<AppLayout
			pageTitle="contract:add_contract"
			namespaces={["contract"]}
			breadcrumbs={[BREADCRUMB.CONTRACT, BREADCRUMB.ADD_CONTRACT]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate(); 

	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Tạo hợp đồng cá nhân</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddContract/>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddContract() {
    const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();
	
    const hookForm = createForm({
		initialValues: {
            contractNumber: "",
            beneficiaryName: "",
            address: "",
            name: "",
            phoneNumber: "",
            taxCode: "",
            contractType: CONTRACT_TYPE.DEBT,
            contractValue: 0,
            startTime: null,
            endTime: null,
            signingDate: null,
            expireDate: null,
            attachments: [""],
            extendAttachments: [""],
            products: [{}],
            isStoreContractDocument: false,
		},
		validate: (values) => validateContractForm(values,false, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
            const payload = data;
            payload.attachments = payload.attachments.filter((e) => e.length > 0);
            payload.extendAttachments = payload.extendAttachments.filter((e) => e.length > 0);
            payload.products.forEach((e) => {
                e.productID = +e.productID;
            })

            const res = await createContract(payload);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create contract:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.create_success`);
			    navigate("/contract", { replace: false });
            }

            return;
		},
	});

	return (
		<ContractIndividualFormCreate hookForm={hookForm}/>
	);
}