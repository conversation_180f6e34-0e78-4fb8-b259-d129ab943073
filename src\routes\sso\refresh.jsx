import { json, parseCookie } from "solid-start";
import { getAccessTokenByRefresh } from "~/services/iam/iam.client";

/**
 *
 * @param {object} param0
 * @param {Request} param0.request
 * @returns
 */
export async function POST({ request }) {
	const cookieStr = request.headers.get("Cookie");
	const cookie = parseCookie(cookieStr ?? "");
	const curRFToken = cookie["refresh_token"];

	request.headers.delete("Cookie");

	const res = await getAccessTokenByRefresh(request, curRFToken);
	if (res.status != "OK") {
		return json(res);
	}
	const d = res.status === "OK" ? res.data[0] : null;

	return json(
		{
			status: "OK",
			message: "refresh access token successfully",
		},
		{
			headers: {
				// https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie
				"Set-Cookie": [
					`session_token=${d.accessToken}; Path=/; HttpOnly; Max-Age=99999999`,
					`refresh_token=${d.refreshToken}; Path=/sso/refresh; HttpOnly; Max-Age=99999999`,
				].join(","),
			},
		}
	);
}

export default () => {
	return (
		<>
			<p>Page check rf token </p>
		</>
	);
};
