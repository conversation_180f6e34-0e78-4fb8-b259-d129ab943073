import {
    <PERSON><PERSON>,
    <PERSON>,
    CardBody,
    Col,
    DatePicker,
    EHTMLType,
    ErrorMessage,
    FileInput,
    FormCheck,
    FormInput,
    FormSelect,
    Row,
    Tooltip,
    useToast,
    useTranslate
} from "@buymed/solidjs-component/components";
import { API_STATUS, formatCurrency, isEmptyObject } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary, Index, Show } from "solid-js";
import { useNavigate } from "solid-start";
import { FormTimeAdornment } from "~/components/FormTimeAdornment";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { useAuth } from "~/contexts/AuthContext";
import { createBid } from "~/services/tender/bid.client";
import { BID_CATEGORY_OPTIONS, BID_SUBMISSION_METHOD, validateBidForm } from "~/services/tender/bid.model";
import { REGION_OPTIONS, YEAR_OPTIONS } from "~/services/tender/region.model";
import SaveIcon from "~icons/mdi/content-save";

export default () => {
	return (
		<AppLayout
			pageTitle="bid:add_bid"
			namespaces={["bid"]}
			breadcrumbs={[BREADCRUMB.BID, BREADCRUMB.ADD_BID]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const { t } = useTranslate();
    
	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">{t`bid:add_bid`}</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddBid/>
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddBid() {
    const { t } = useTranslate();
    const toast = useToast();
    const navigate = useNavigate();
    const { documentToken } = useAuth();

    const haftMonth = new Date().getTime() + 15 * 86400000
    const submissionMonth = new Date().getTime() + (15+180) * 86400000

    const hookForm = createForm({
		initialValues: {
            invitationOfBid: "",
            procuringEntity: "",
            bidName: "",
            address: "",
            contact: "",
            contractExecutionPeriod: 12,
            contractExecutionPeriodKind: "MONTH",
            region: "",
            openingDate: new Date(),
            closingDate: new Date(haftMonth),
            securityValidity: 180,
            securitySubmissionDate: new Date(submissionMonth),
            securitySubmissionMethod: BID_SUBMISSION_METHOD.LETTER_OF_BANK_GUARANTEE,
            mainCategory: BID_CATEGORY_OPTIONS[0],
            numOfProduct: 0,
            bidPrice: 0,
            attachments: [""],
            year: new Date().getFullYear()
		},
		validate: (values) => validateBidForm(values, t),
		onSubmit: async (values) => {
            const data = JSON.parse(JSON.stringify(values));
            data.attachments = data.attachments.filter((e) => e.length > 0);
            data.securityValidity = +data.securityValidity;
            data.year = +data.year;
            
            if(!data.contractTerminationDate) {
                delete data.contractTerminationDate;
            }

            if (!data.performanceSubmissionDate) {
                delete data.performanceSubmissionDate;
            }

            if(!data.contractTerminationDate) {
                delete data.contractTerminationDate;
            }

            const res = await createBid(data);

			if (res.status !== API_STATUS.OK) {
                console.error("[Error] Create bid:", res);
			    toast.error(t("common:notify.action_fail", { error: res.message }));
            } else {
                toast.success(t`common:notify.create_success`);
			    navigate("/bid?tab=0", { replace: false });
            }

            return;
		},
	});

    function updateSecuritySubmissionDate() {
        if (hookForm.data("securitySubmissionMethod") !== BID_SUBMISSION_METHOD.CASH) {
            return
        }
		const securityValidity = hookForm.data('securityValidity') || 0;
		const closingDate = hookForm.data('closingDate');
        const changeDate = new Date(closingDate).getTime() + securityValidity * 86400000;
        const submissionDate = new Date(changeDate);
        console.log(submissionDate);
		hookForm.setFields('securitySubmissionDate', submissionDate); 
	}

	return (
		<Row class="gap-3">
			<form ref={hookForm.form}>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin gói thầu</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="invitationOfBid"
                                        id="invitationOfBid"
                                        label="Mã gói thầu"
                                        placeholder="Nhập mã mời thầu"
                                        invalid={!isEmptyObject(hookForm.errors("invitationOfBid"))}
										feedbackInvalid={hookForm.errors("invitationOfBid")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={8}>
                                    <FormInput
                                        name="bidName"
                                        id="bidName"
                                        label="Tên gói thầu"
                                        invalid={!isEmptyObject(hookForm.errors("bidName"))}
										feedbackInvalid={hookForm.errors("bidName")}
                                        required
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="procuringEntity"
                                        id="procuringEntity"
                                        label="Bên mời thầu"
                                        placeholder="Nhập bên mời thầu"
                                        invalid={!isEmptyObject(hookForm.errors("procuringEntity"))}
										feedbackInvalid={hookForm.errors("procuringEntity")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="address"
                                        id="address"
                                        label="Địa chỉ"
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormInput
                                        name="contact"
                                        id="contact"
                                        label="Thông tin liên lạc"
                                        invalid={!isEmptyObject(hookForm.errors("contact"))}
										feedbackInvalid={hookForm.errors("contact")}
                                    />
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={4}>
                                    <FormTimeAdornment
                                        name="contractExecutionPeriod"
                                        id="contractExecutionPeriod"
                                        label="Thời gian thực hiện hợp đồng"
                                        type="number"
                                        kindCodeName="contractExecutionPeriodKind"
                                        invalid={!isEmptyObject(hookForm.errors("contractExecutionPeriod"))}
										feedbackInvalid={hookForm.errors("contractExecutionPeriod")}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="region"
                                        id="region"
                                        options={REGION_OPTIONS(t)}
                                        placeholder="Chọn"
                                        label="Khu vực"
                                        invalid={!isEmptyObject(hookForm.errors("region"))}
										feedbackInvalid={hookForm.errors("region")}
                                        required
                                        onchange={(e) => {
                                            // hookForm.setData("region", e.target.value);
                                        }}
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={4}>
                                    <FormSelect
                                        name="year"
                                        id="year"
                                        options={YEAR_OPTIONS()}
                                        placeholder="Chọn"
                                        label="Năm"
                                        invalid={!isEmptyObject(hookForm.errors("year"))}
										feedbackInvalid={hookForm.errors("year")}
                                        required
                                    />
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Thông tin dự thầu</header>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <DatePicker
										label={t`bid:bid_opening_date`}
										name="openingDate"
                                        id="openingDate"
                                        format="dd/MM/yyyy"
                                        invalid={!isEmptyObject(hookForm.errors("openingDate"))}
										feedbackInvalid={hookForm.errors("openingDate")}
										required
									/>
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <DatePicker
										label={t`bid:bid_closing_date`}
										name="closingDate"
                                        id="closingDate"
                                        format="dd/MM/yyyy"
                                        onDateChange={() => updateSecuritySubmissionDate()}
                                        invalid={!isEmptyObject(hookForm.errors("closingDate"))}
										feedbackInvalid={hookForm.errors("closingDate")}
										required
									/>
                                </Col>
                            </Row>
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name="securityValidity"
                                        id="securityValidity"
                                        label="Hiệu lực bảo đảm dự thầu"
                                        endAdornment={"Ngày"}
                                        onChange={() => updateSecuritySubmissionDate()}
                                        invalid={!isEmptyObject(hookForm.errors("securityValidity"))}
										feedbackInvalid={hookForm.errors("securityValidity")}
                                        required

                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <label for="securitySubmissionMethod"></label>
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="securitySubmissionMethod"
                                        label="Tiền mặt"
                                        value={BID_SUBMISSION_METHOD.CASH}

                                    />
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="securitySubmissionMethod"
                                        label="Thư đảm bảo"
                                        value={BID_SUBMISSION_METHOD.LETTER_OF_BANK_GUARANTEE}
                                    />
                                </Col>
                                <Show when={hookForm.data("securitySubmissionMethod") == BID_SUBMISSION_METHOD.CASH}>
                                    <Col xs={12} md={6} lg={3}>
                                        <FormInput
                                            name="securitySubmissionAmount"
                                            id="securitySubmissionAmount"
                                            label="Số tiền đảm bảo dự thầu"
                                            type="number"
                                            text={hookForm.data("securitySubmissionAmount") ? formatCurrency(hookForm.data("securitySubmissionAmount")) : ""}
                                            invalid={!isEmptyObject(hookForm.errors("securitySubmissionAmount"))}
                                            feedbackInvalid={hookForm.errors("securitySubmissionAmount")}
                                        />
                                    </Col>
                                    <Col xs={12} md={6} lg={3}>
                                        <DatePicker
                                            label="Thời gian hết hạn đảm bảo dự thầu"
                                            name="securitySubmissionDate"
                                            id="securitySubmissionDate"
                                            format="dd/MM/yyyy"
                                            invalid={!isEmptyObject(hookForm.errors("securitySubmissionDate"))}
                                            feedbackInvalid={hookForm.errors("securitySubmissionDate")}
                                        />
                                    </Col>
                                </Show>
                            </Row>
                            {/* <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name="performanceValidity"
                                        id="performanceValidity"
                                        label="Hiệu lưc đảm bảo hợp đồng"
                                        endAdornment={"Ngày"}
                                        invalid={!isEmptyObject(hookForm.errors("performanceValidity"))}
										feedbackInvalid={hookForm.errors("performanceValidity")}
                                        required

                                    />
                                </Col>
                                <Col xs={12} md={6} lg={3}>
                                    <label for="performanceSubmissionMethod"></label>
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="performanceSubmissionMethod"
                                        label="Tiền mặt"
                                        value={BID_SUBMISSION_METHOD.CASH}

                                    />
                                    <FormCheck
                                        type="radio"
                                        color="success"
                                        name="performanceSubmissionMethod"
                                        label="Thư đảm bảo"
                                        value={BID_SUBMISSION_METHOD.LETTER_OF_BANK_GUARANTEE}
                                    />
                                </Col>
                                <Show when={hookForm.data("performanceSubmissionMethod") == BID_SUBMISSION_METHOD.CASH}>
                                    <Col xs={12} md={6} lg={3}>
                                        <FormInput
                                            name="performanceSubmissionAmount"
                                            id="performanceSubmissionAmount"
                                            label="Số tiền đảm bảo hợp đồng"
                                            type="number"
                                            text={hookForm.data("performanceSubmissionAmount") ? formatCurrency(hookForm.data("performanceSubmissionAmount")) : ""}
                                            invalid={!isEmptyObject(hookForm.errors("performanceSubmissionAmount"))}
                                            feedbackInvalid={hookForm.errors("performanceSubmissionAmount")}
                                            required
                                        />
                                    </Col>
                                    <Col xs={12} md={6} lg={3}>
                                        <DatePicker
                                            label="Thời gian hết hạn đảm bảo hợp đồng"
                                            name="performanceSubmissionDate"
                                            id="performanceSubmissionDate"
                                            invalid={!isEmptyObject(hookForm.errors("performanceSubmissionDate"))}
                                            feedbackInvalid={hookForm.errors("performanceSubmissionDate")}
                                            required
                                        />
                                    </Col>
                                </Show>
                            </Row> */}
                            <Row class="row-gap-3">
                                <Col xs={12} md={6} lg={3}>
                                    <FormSelect
                                        name="mainCategory"
                                        id="mainCategory"
                                        label={t`bid:main_category`}
                                        options={BID_CATEGORY_OPTIONS(t)}
                                        required
                                    />
                                </Col>
                                {/* <Col xs={12} md={6} lg={3}>
                                    <FormInput
                                        name="numOfProduct"
                                        id="numOfProduct"
                                        label={t`bid:bid_quantity`}
                                        type="number"
                                        invalid={!isEmptyObject(hookForm.errors("numOfProduct"))}
										feedbackInvalid={hookForm.errors("numOfProduct")}
                                        required
                                    />
                                </Col>
                                <Col xs={12} md={6} lg={6}>
                                    <FormInput
                                        name="bidPrice"
                                        id="bidPrice"
                                        label={t`bid:bid_price`}
                                        type="number"
                                        text={hookForm.data("bidPrice") ? formatCurrency(hookForm.data("bidPrice")) : ""}
                                        invalid={!isEmptyObject(hookForm.errors("bidPrice"))}
										feedbackInvalid={hookForm.errors("bidPrice")}
                                        required
                                    />
                                </Col> */}
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Card>
                    <CardBody>
                        <section class="d-flex flex-column row-gap-3">
                            <header class="section-header">Tài liệu đính kèm</header>
                            <Row class="row-gap-3">
                                <Col xs={12} >
                                    <Index each={hookForm.data("attachments")}>
                                        {(attachment, index) => (
                                            <FileInput
                                                documentToken={documentToken()}
                                                mode={EHTMLType.Documents}
                                                type={EHTMLType.Documents}
                                                extensions=".xlsx, .xls, .doc, .docx, .ppt, .pptx, .pdf"
                                                onAdd={(newFile) => {
                                                    console.log("newFile=", newFile)
                                                    hookForm.setData("attachments", [
                                                        newFile.previewLink,
                                                        ...hookForm.data("attachments"),
                                                    ]);
                                                }}
                                                onRemove={() =>
                                                    hookForm.setData(
                                                        "attachments",
                                                        hookForm.data("attachments")?.filter(
                                                            (_, i) => i !== index
                                                        )
                                                    )
                                                }
                                                value={attachment}
                                            />
                                        )}
                                    </Index>
                                </Col>
                            </Row>
                        </section>
                    </CardBody>
                </Card>
                <br/>
                <Show when={true}>
                    <div class="submit-wrapper">
                        <Button color="secondary" type="button">
                            {t`common:button.cancel`}
                        </Button>
                        <Tooltip content={t`common:button.save`}>
                            <Button color="success" class="ms-2" type="submit">
                                <SaveIcon class="fs-5" />
                                {t`common:button.save`}
                            </Button>
                        </Tooltip>
                    </div>
                </Show>

            </form>
		</Row>
	);
}