import {
	<PERSON><PERSON>,
	Card,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
	Tooltip,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { Index, Show } from "solid-js";
import { A } from "solid-start";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import BMTablePagination from "~/components/table/BMTablePagination";
import { BID_STATUS_LABEL_COLOR } from "~/services/tender/bid.model";
import { CONTRACT_STATUS_LABEL, CONTRACT_STATUS_LABEL_COLOR } from "~/services/tender/contract.model";
import { formatEllipsis } from "~/utils/format";
import EditIcon from "~icons/mdi/square-edit-outline";
import MdiStickerTextOutline from '~icons/mdi/sticker-text-outline';

/**
 * @param {object} props
 * @param {import("~/services/tender/contract.model").Contract[]} props.contracts
 * @param {import("~/services/tender/bid.model").Bid[]} props.bidMap
 * @param {number} props.total
 * @returns {import("solid-js").JSXElement}
 */
export function ContractTable(props) {
	const { t } = useTranslate();

	return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`contract:no`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:contract_number`}</TableHeaderCell>
						<TableHeaderCell>Tên hợp đồng</TableHeaderCell>
                        <TableHeaderCell>{t`contract:beneficiary_name`}</TableHeaderCell>
						<TableHeaderCell>Đơn vị mời thầu</TableHeaderCell>
						<TableHeaderCell>{t`contract:invitation_of_bid`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:signing_date`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:expire_date`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:payment_to_date`}</TableHeaderCell>
						<TableHeaderCell>{t`contract:contract_value`}</TableHeaderCell>
						<TableHeaderCell class="col-2">
							<div class="d-flex justify-content-center align-items-center gap-1">
								{t`contract:contract_status`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.contracts}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`contract:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(contract) => <ContractTableRow item={contract()} bid={props.bidMap}/>}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

/**
 *
 * @param {object} props
 * @param {object} props.bid
 * @param {import("~/services/tender/contract.model").Contract} props.item
 * @returns {import("solid-js").JSXElement}
 */
function ContractTableRow(props) {
	const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.item.contractID}</TableCell>
			<TableCell>
                <A
                    href={`/contract/${props.item.code}`}
                >
                    {props.item.contractNumber}
                </A>
            </TableCell>
			<TableCell>{props.item.name}</TableCell>
			<TableCell>{props.item.beneficiaryName}</TableCell>
			<TableCell>
				<Show when={props.bid[props.item.bidID]}>
					{props.bid[props.item.bidID].procuringEntity || "-"}
				</Show>
			</TableCell>
			<TableCell>
				<Show when={props.bid[props.item.bidID]}>
					<A
						href={`/bid/${props.item.bidID}/info`}
					>
						[{props.bid[props.item.bidID].itb}] - {formatEllipsis(props.bid[props.item.bidID].bidName, 50)}
					</A>
				</Show>
			</TableCell>
			<TableCell>{props.item.signingDate ? formatDatetime(props.item.signingDate, "dd/MM/yyyy") : ""}</TableCell>
			<TableCell>{props.item.expireDate ? formatDatetime(props.item.expireDate, "dd/MM/yyyy") : ""}</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.currentAmount)}
				</div>
			</TableCell>
			<TableCell>
				<div style={{ "text-align": "right" }}>
					{formatNumber(props.item.debtLimit)}
				</div>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Button color={CONTRACT_STATUS_LABEL_COLOR[props.item.status]} class="m-3">
						{t(CONTRACT_STATUS_LABEL[props.item.status])}
					</Button>
				</div>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<AuthContent privilege={PRIVILEGE.CONTRACT.VIEW}>
						<Tooltip content={t`contract:edit_contract`}>
							<Show when={props.item.bidID}>
								<Button
									class="p-2"
									variant="outline"
									color="primary"
									startIcon={<EditIcon />}
									href={`/contract/${props.item.code}`}
								/>
							</Show>
							<Show when={!props.item.bidID}>
								<Button
									class="p-2"
									color="primary"
									variant="outline"
									startIcon={<EditIcon />}
									href={`/contract-individual/${props.item.code}`}
								/>
							</Show>
						</Tooltip>
					</AuthContent>
					{/* <AuthContent privilege={PRIVILEGE.CONTRACT.VIEW_PRODUCTS}>
						<Tooltip content={t`contract:view_list_contract_annex`}>
							<Button
								class="p-2"
								variant="outline"
								color="secondary"
								startIcon={<MdiStickerTextOutline />}
								href={`/contract/${props.item.code}/annex`}
							/>
						</Tooltip>
					</AuthContent> */}
					{/* <AuthContent privilege={PRIVILEGE.CONTRACT.VIEW_PRODUCTS}>
						<Tooltip content={t`contract:view_list_product`}>
							<Button
								class="p-2"
								variant="outline"
								color="secondary"
								startIcon={<MdiStickerTextOutline />}
								href={`/product?q=${JSON.stringify({contractCode: props.item.code, contractNo: props.item.contractNumber})}`}
							/>
						</Tooltip>
					</AuthContent> */}
				</div>
			</TableCell>
		</TableRow>
	);
}
