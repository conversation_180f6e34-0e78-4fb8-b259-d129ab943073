{"name": "fe-skeleton", "license": "ISC", "scripts": {"dev": "solid-start dev", "build": "solid-start build", "format": "prettier --cache -w .", "lint": "eslint \"src/**/*.{js,jsx}\"", "start": "solid-start start -p 80", "start-local": "solid-start start -p 3000"}, "type": "module", "devDependencies": {"@types/node": "^20.8.0", "esbuild": "^0.19.11", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-solid": "^0.13.1", "postcss": "^8.4.33", "prettier": "^3.2.2", "sass": "^1.69.7", "solid-start-node": "^0.3.5", "vite": "^4.5.2"}, "dependencies": {"@buymed/solidjs-component": "git+https://gitlab.buymed.tech/sdk/solidjs/buymed-component#759eb11a104232187d49dd1bac1d938ba598238d", "@felte/solid": "^1.2.13", "@iconify-json/mdi": "^1.1.49", "@solidjs/meta": "^0.29.3", "@solidjs/router": "^0.9.1", "@buymed/base-fe": "git+https://gitlab.buymed.tech/sdk/solidjs/base-fe#a6c1f9459db20e94aca309a3625c03c712d93415", "js-cookie": "^3.0.1", "object.hasown": "^1.1.3", "ofetch": "^1.3.3", "solid-js": "^1.8.12", "solid-start": "^0.3.10", "unplugin-icons": "^0.18.2"}, "engines": {"node": ">=18.14.2"}}