import { API_STATUS } from "@buymed/solidjs-component/utils";
import { productUnitConvert } from "./product.client";

export const ORDER_STATE = {
    WAIT_TO_CONFIRM: "WAIT_TO_CONFIRM",
	CONFIRMED: "CONFIRMED",
    PROCESSING: "PROCESSING",
    WAIT_TO_DELIVER: "WAIT_TO_DELIVER",
    DELIVERING: "DELIVERING",
    DELIVERED: "DELIVERED",
    COMPLETED: "COMPLETED",
    CANCELLED: "CANCELLED",
    DELETING: "DELETING",
} as const;

export const ORDER_STATUS_LABEL_COLOR = {
    [ORDER_STATE.CONFIRMED]: "info",
    [ORDER_STATE.PROCESSING]: "primary",
    [ORDER_STATE.DELETING]: "primary",
    [ORDER_STATE.WAIT_TO_DELIVER]: "primary",
    [ORDER_STATE.DELIVERING]: "primary",
    [ORDER_STATE.DELIVERED]: "primary",
	[ORDER_STATE.COMPLETED]: "success",
    [ORDER_STATE.CANCELLED]: "danger",
} as const;

export function OrderStatusLabelColor(value) : string {
    if(typeof(ORDER_STATUS_LABEL_COLOR[value]) != "undefined") {
        return ORDER_STATUS_LABEL_COLOR[value]
    }
    return "warning";
}

export const ORDER_STATE_LABEL = {
	[ORDER_STATE.WAIT_TO_CONFIRM]: "order:state.WAIT_TO_CONFIRM",
    [ORDER_STATE.CONFIRMED]: "order:state.CONFIRMED",
    [ORDER_STATE.DELETING]: "order:state.DELETING",
    [ORDER_STATE.PROCESSING]: "order:state.PROCESSING",
    [ORDER_STATE.WAIT_TO_DELIVER]: "order:state.WAIT_TO_DELIVER",
    [ORDER_STATE.DELIVERING]: "order:state.DELIVERING",
    [ORDER_STATE.DELIVERED]: "order:state.DELIVERED",
    [ORDER_STATE.COMPLETED]: "order:state.COMPLETED",
    [ORDER_STATE.CANCELLED]: "order:state.CANCELLED",
} as const;


// ===================================================
// Validation
// ===================================================
export async function validateOrderForm(values, t) {
	const err: any = {};

    if (!values.receiverName || values.receiverName.length == 0) {
        err.receiverName = t`order:receiver_name_required`;
    }

    if (!values.phoneNumber || values.phoneNumber.length == 0) {
        err.phoneNumber = t`order:phone_number_required`;
    }

    if (!values.provinceCode || values.provinceCode.length == 0) {
        err.provinceCode = t`order:province_code_required`;
    }

    if (!values.districtCode || values.districtCode.length == 0) {
        err.districtCode = t`order:district_code_required`;
    }

    if (!values.wardCode || values.wardCode.length == 0) {
        err.wardCode = t`order:ward_code_required`;
    }

    if (!values.shippingAddress || values.shippingAddress.length == 0) {
        err.shippingAddress = t`order:shipping_address_required`;
    }

    // fetch product unit
    if(values.products && values.products.length > 0) {
        const mapProductUnit = {};
        const productUnit = values.products.map((e, idx) => {
            mapProductUnit[+e.productID] = idx;
            return {
                productID: +e.productID,
                fromUnit: e.unit,
                fromQuantity: +e.quantity
            }
        })

        const productUnitAvailable = productUnit.filter((e) => e.fromQuantity > 0);
        if(productUnitAvailable.length > 0) {
            const payloadProductUnit = {
                products: productUnitAvailable,
            };
            const resProductUnit = await productUnitConvert(payloadProductUnit)
            if (resProductUnit.status === API_STATUS.OK) {
                const productUnitData = resProductUnit.data;
                productUnitData.forEach(e => {
                    if(mapProductUnit[e.productID] >= 0) {
                        if(e.remainderQuantity && e.remainderQuantity > 0) {
                            err[`products.${mapProductUnit[e.productID]}.unit`] = `${e.rate[0] || ""} ${e.toUnitName} ${e.rate[1] || ""} ${e.fromUnitName}, ${e.fromQuantity} ${e.fromUnitName} -> ${e.toQuantity} ${e.toUnitName}, lẻ ${e.remainderQuantity} ${e.remainderUnit}`
                        }
                    }
                })
            }
        }
    }
    
	return err;
}