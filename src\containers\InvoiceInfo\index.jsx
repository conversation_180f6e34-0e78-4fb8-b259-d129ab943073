import {
	<PERSON><PERSON>,
	Col,
	Row
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { A } from "@solidjs/router";
import { createResource, Show } from "solid-js";
import { getInvoice } from "~/services/oms/order.client";
import { InvoiceStatusLabelColor } from "~/services/oms/order.model";
import { getCDNImageURL } from "~/utils/format";

export function InvoiceInfo(props) {
    const [invoiceInfo] = createResource(
		() => [props.internalOrderID, props.invoiceNo],
        async ([internalOrderID, invoiceNo]) => {
			if(!invoiceNo || invoiceNo.length == 0) {
				return;
			}

			const res = await getInvoice(
				{
					internalOrderID
				}
			);

			if (res.status !== API_STATUS.OK) {
				return {};
			}

            return res.data[0]
		},
		{ initialValue: {}} 
    )

	return (
        <>
            <Row class="row-gap-3">
                <Col xs={12} md={6} lg={4} style={{"font-weight": "bold"}}>
                    <p>Mã hoá đơn</p>
                </Col>
                <Col xs={12} md={6} lg={6}>
                    <Show when={props.invoiceNo} fallback={<p>-</p>}>
                        <p>{props.invoiceNo}</p>
                    </Show>
                </Col>
            </Row>
			<Show when={invoiceInfo() && invoiceInfo().status}>
				<Row class="row-gap-3">
					<Col xs={12} md={6} lg={4} style={{"font-weight": "bold"}}>
						<p>Trạng thái</p>
					</Col>
					<Col xs={12} md={6} lg={6}>
						<Button color={InvoiceStatusLabelColor(invoiceInfo().status)}>
							{invoiceInfo().status}
						</Button>
					</Col>
				</Row>
				<Row class="row-gap-3">
					<Col xs={12} md={6} lg={4} style={{"font-weight": "bold"}}>
						<p>PDF</p>
					</Col>
					<Show when={invoiceInfo().invoiceData && invoiceInfo().invoiceData[0] && invoiceInfo().invoiceData[0].pdfUrl}>
						<Col xs={12} md={6} lg={3}>
							<A target="_blank" href={getCDNImageURL(invoiceInfo().invoiceData[0].pdfUrl)}>Xem hoá đơn</A>
							<br/>
							<br/>
							<A target="_blank" href="#">Yêu cầu hoá đơn chuyển đổi</A>
						</Col>
					</Show>
				</Row>
			</Show>
        </>
    );
}
