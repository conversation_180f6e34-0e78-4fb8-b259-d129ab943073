import { APIResponse } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";

export async function getAddressInfo({
	wardCode,
    countryCode,
}: {
	wardCode?: string;
    countryCode?: string;
}): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.GET, `/core/master-data/v1/ward/list`, {
		wardCode,
        countryCode,
	});
}