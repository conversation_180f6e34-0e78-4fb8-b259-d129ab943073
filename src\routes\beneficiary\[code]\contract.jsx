import {
	Col,
	DEFAULT_LIMIT,
	DEFAULT_PAGE,
	Row,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS, toQueryObject } from "@buymed/solidjs-component/utils";
import { createResource } from "solid-js";
import { createRouteData, useParams, useRouteData, useSearchParams } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { HeaderDetail } from "~/containers/Benaficiary/header-detail";
import { ContractTable } from "~/containers/ContractTable";
import { getBidList } from "~/services/tender/bid.client";
import { getContractList } from "~/services/tender/contract.client";
import { CONTRACT_STATUS } from "~/services/tender/contract.model";
import { callMultilRequest, chunkArrays } from "~/utils/func";

export function routeData({ location, params }) {
	return createRouteData(
		async ([query]) => {
			const beneficiaryCode = params.code;
			const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};
			q.beneficiaryCode = beneficiaryCode;

			switch (query.tab) {
				case "1": {
					q.status = CONTRACT_STATUS.ACTIVE;
					break;
				}
				case "2": {
					q.status = CONTRACT_STATUS.DONE;
					break;
				}
				case "3": {
					q.status = CONTRACT_STATUS.FINISH;
					break;
				}
				case "4": {
					q.status = CONTRACT_STATUS.DRAFT;
					break;
				}
			}

			const resContract = await getContractList({
				q,
				offset,
				limit,
				option: {
					total: true,
				},
			});

			let bidIDs = [];
			(resContract.data || [])?.forEach((contract) => {
				bidIDs = bidIDs.concat(contract.bidID || []);
			});
			bidIDs = Array.from(new Set(bidIDs));
			const bidMap = {};

			if (bidIDs.length > 0) {
				const chunks = chunkArrays(bidIDs, 100);

				const { data } = await callMultilRequest(chunks, async (payload, result) => {
					const res = await getBidList({
						q: { bidIDs: payload },
						offset: 0,
					});

					if (res.status === API_STATUS.OK) {
						result.data.push(...(res.data ?? []));
					} else {
						result.errors.push(res);
						result.stop = true;
					}

					return res;
				});

				(data || []).forEach((item) => (bidMap[item.bidID] = item));
			}

			return {
				data: resContract.data,
				total: resContract.total,
				bidMap,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout
			pageTitle="beneficiary:edit"
			namespaces={["beneficiary", "contract"]}
			breadcrumbs={[BREADCRUMB.BENEFICIARY, BREADCRUMB.EDIT_BENEFICIARY]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	const pageData = useRouteData();
	const [searchParams] = useSearchParams();
	const { t } = useTranslate();
	const params = useParams();
	const [tabs] = createResource(
		() => toQueryObject(searchParams).q || "{}",
		async (qString) => {
			const q = JSON.parse(qString);
			q.beneficiaryCode = params.code;
			const totalRes = await Promise.all([
				getContractList({
					q,
					limit: 1,
					option: { total: true },
				}),
				getContractList({
					q: { ...q, status: CONTRACT_STATUS.ACTIVE },
					limit: 1,
					option: { total: true },
				}),
				getContractList({
					q: { ...q, status: CONTRACT_STATUS.DONE },
					limit: 1,
					option: { total: true },
				}),
				getContractList({
					q: { ...q, status: CONTRACT_STATUS.FINISH },
					limit: 1,
					option: { total: true },
				}),
				getContractList({
					q: { ...q, status: CONTRACT_STATUS.DRAFT },
					limit: 1,
					option: { total: true },
				}),
			]);
			const totals = totalRes.map((res) => res.total || 0);

			return [
				t`contract:status.all` + ` (${totals[0]})`,
				t`contract:status.ACTIVE` + ` (${totals[1]})`,
				t`contract:status.DONE` + ` (${totals[2]})`,
				t`contract:status.FINISH` + ` (${totals[3]})`,
				t`contract:status.DRAFT` + ` (${totals[4]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<div class="d-flex justify-content-start">
					<h1 class="page-title">{t`contract:contract_list`}</h1>
				</div>
			</Col>
			<HeaderDetail
				tabs={[
					{
						name: "Thông tin",
						href: "info",
					},
					{
						name: "Hợp đồng",
						href: "contract",
					},
					{
						name: "Đơn hàng",
						href: "order",
					},
				]}
			/>
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				<ContractTable
					contracts={pageData()?.data}
					total={pageData()?.total}
					bidMap={pageData()?.bidMap}
				/>
			</Col>
		</Row>
	);
}
